/**
 * Protobuf WebSocket 使用示例
 * 
 * 这个文件展示了如何使用支持protobuf的WebSocket连接
 */

// 导入Vue的watch函数
import { watch } from 'vue';
import { useWebSocket } from '@/composables/useWebSocket';
import { SocketEventCode } from '@/constant/gameData';
import emitter from '@/utils/emitter';

// 示例：创建WebSocket连接
export function createProtobufWebSocketConnection() {
  const token = 'your-auth-token';
  const socketUrl = `${import.meta.env.VITE_SOCKET_URL}/websocket?token=${token}`;
  
  const { 
    socket, 
    isConnected, 
    protobufReady, 
    connect, 
    disconnect, 
    send, 
    subscribe, 
    unsubscribe 
  } = useWebSocket(socketUrl, (ws) => {
    console.log('WebSocket connected with protobuf support');
    
    // 连接成功后发送初始化请求
    send(SocketEventCode.SOCKET_INIT);
    send(SocketEventCode.GET_GAME_CONFIG);
    send(SocketEventCode.GET_USER_BALANCE);
  });

  // 订阅游戏事件
  subscribe(SocketEventCode.SOCKET_INIT, (data) => {
    console.log('Socket initialized:', data);
    emitter.emit('socket-init', data);
  });

  subscribe(SocketEventCode.BET, (data) => {
    console.log('Betting started:', data);
    emitter.emit('game-bet', data);
  });

  subscribe(SocketEventCode.GAME_START, (data) => {
    console.log('Game started:', data);
    emitter.emit('game-start', data);
  });

  subscribe(SocketEventCode.GAME_END, (data) => {
    console.log('Game ended:', data);
    emitter.emit('game-end', data);
  });

  subscribe(SocketEventCode.SETTLEMENT, (data) => {
    console.log('Game settlement:', data);
    emitter.emit('game-settlement', data);
  });

  // 用户投注
  const placeBet = (playTypeId: string, amount: number) => {
    if (isConnected.value && protobufReady.value) {
      const betData = {
        playTypeId,
        amount,
        roundId: 'current-round-id'
      };
      
      send(SocketEventCode.USER_BET, betData);
    } else {
      console.error('WebSocket not connected or protobuf not ready');
    }
  };

  // 撤销投注
  const revokeBet = (playTypeId: string) => {
    if (isConnected.value && protobufReady.value) {
      const revokeData = {
        playTypeId,
        roundId: 'current-round-id'
      };
      
      send(SocketEventCode.USER_REVOKE, revokeData);
    } else {
      console.error('WebSocket not connected or protobuf not ready');
    }
  };

  // 获取用户余额
  const getUserBalance = () => {
    if (isConnected.value && protobufReady.value) {
      send(SocketEventCode.GET_USER_BALANCE);
    }
  };

  // 获取游戏配置
  const getGameConfig = () => {
    if (isConnected.value && protobufReady.value) {
      send(SocketEventCode.GET_GAME_CONFIG);
    }
  };

  // 启动连接
  connect();

  return {
    socket,
    isConnected,
    protobufReady,
    connect,
    disconnect,
    send,
    subscribe,
    unsubscribe,
    placeBet,
    revokeBet,
    getUserBalance,
    getGameConfig
  };
}

// 示例：监听连接状态
export function monitorWebSocketStatus() {
  const connection = createProtobufWebSocketConnection();
  
  // 监听连接状态
  watch(connection.isConnected, (connected) => {
    if (connected) {
      console.log('✅ WebSocket connected');
    } else {
      console.log('❌ WebSocket disconnected');
    }
  });

  // 监听protobuf状态
  watch(connection.protobufReady, (ready) => {
    if (ready) {
      console.log('✅ Protobuf ready');
    } else {
      console.log('⏳ Protobuf initializing...');
    }
  });

  return connection;
}

// 示例：错误处理
export function handleWebSocketErrors() {
  const connection = createProtobufWebSocketConnection();
  
  // 添加错误处理
  if (connection.socket.value) {
    connection.socket.value.onerror = (error) => {
      console.error('WebSocket error:', error);
      // 可以在这里添加错误上报逻辑
    };
    
    connection.socket.value.onclose = (event) => {
      console.log('WebSocket closed:', event.code, event.reason);
      // 可以在这里添加重连逻辑
    };
  }
  
  return connection;
}

