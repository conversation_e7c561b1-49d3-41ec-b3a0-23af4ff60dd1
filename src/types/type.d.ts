declare global {
  interface Window {
  }
}

declare interface SocketResData {
  msgId: string;
  data: string;
}

declare interface SocketData {
  currentTimeMillis: number;
  data: any;
  msgId: string;
  path: string;
  serverName: string;
}

declare type MessageHandler = (data: SocketData) => void;

declare interface SoundConfig {
  // 音频的唯一标识符，用于在音频管理器中引用特定的音频
  id: string;

  // 音频文件的路径或URL（例如 'background_music.mp3'），支持MP3、OGG等格式
  src: string;

  // 是否循环播放音频，默认为 false。常用于背景音乐（true）或一次性音效（false）
  loop?: boolean;

  // 音频的初始音量，范围 0.0 到 1.0，默认为 1.0。用于设置默认播放音量
  volume?: number;

  // 音频池大小，指定并发播放的音频实例数，默认为 1。适用于频繁触发的音效（如跳跃音效）
  poolSize?: number;
}