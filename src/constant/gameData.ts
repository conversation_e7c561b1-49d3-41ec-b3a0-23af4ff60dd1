
export enum SocketEventCode {
    /** 过渡时期 */
    WAIT = "0",
    /** 下注时期 */
    BET = "100",
    /** 下注结束 */
    BET_END = "200",
    /** 开始游戏 */
    GAME_START = "300",
    /** 结束游戏 */
    GAME_END = "400",
    /** 结算 */
    SETTLEMENT = "500",

    /** 游戏暂停 */
    GAME_PAUSED = "800",
    /** 刷新游戏配置 */
    REFRESH_CONFIG = "900",
    /** 广播某用户中大奖 */
    NOTICE_BIG_PRIZE = '600',

    /** 通知当前用户中奖 */
    NOTICE_ONE_PRIZE = '700',
    /** 游戏各区域投注金额 */
    BET_AMOUNT = "1000",

    /** 用户投注 */
    USER_BET = "1001",
    /** 用户撤销 */
    USER_REVOKE = "1002",

    /** 连接socket初始化 */
    SOCKET_INIT = "1005",

    /** 获取用户余额 */
    GET_USER_BALANCE = "1003",
    /** 获取游戏配置 */
    GET_GAME_CONFIG = "1004",
    /** 游戏期数结果 */
    GET_GAME_HISTORY = "1006",
    /** 获取游戏历史投注结果 */
    GET_GAME_HISTORY_BET = "1007",
    /** 获取当前用户投注 */
    GET_PLAYER_BET = "1008",
    /** 获取游戏规则信息 */
    GET_GAME_RULES = "1009",

    /** 心跳-发送 */
    PING = "2000",
    /** 心跳-接收 */
    PONG = "2001",
}

export enum GameStatus {
    // The game is waiting to start, typically in an idle or lobby state.
    GAME_WAIT = "game-wait",
    // The game is in the betting phase, allowing players to place their bets.
    GAME_BET = "game-bet",
    // The betting phase has ended, and no further bets can be placed.
    GAME_BET_END = "game-bet-end",
    // The game has started, transitioning from preparation to active play.
    GAME_START = "game-start",
    // The game has concluded, but final actions like scoring or settlement may still occur.
    GAME_END = "game-end",
    // The game is in the settlement phase, where results are finalized and payouts are processed.
    GAME_SETTLEMENT = "game-settlement",
}

export enum CameraType {
    A = 'a',
    B = 'b',
}

export enum SOUNDS {
    SOUND_1 = "/audio/sound1.mp3",//点击按钮
    SOUND_2 = "/audio/sound2.mp3",//取消按钮
    SOUND_3 = "/audio/sound3.mp3",//系统弹窗音效
    SOUND_4 = "/audio/sound4.mp3",//气泡弹窗
    SOUND_5 = "/audio/sound5.mp3",//玩家投注时音效
    SOUND_6 = "/audio/sound6.mp3",//start betting-投注区打开
    SOUND_7 = "/audio/sound7.mp3",//气泡弹窗
    SOUND_8 = "/audio/sound8.mp3",//转场动画音效1
    SOUND_9 = "/audio/sound9.mp3",//玩家本金增加音效
    SOUND_10 = "/audio/sound10.mp3",//结算阶段-投注区筹码飞向总赢分的过程音效
    SOUND_11 = "/audio/sound11.mp3",//结算阶段-投注区筹码飞到总赢分音效
    SOUND_12 = "/audio/sound12.mp3",//倒计时音效-最后5秒
    SOUND_13 = "/audio/sound13.mp3",//转场动画音效2-在游戏结束后到结算阶段前
    SOUND_14 = "/audio/sound14.mp3",//游戏开始预备跑的音效
    SOUND_15 = "/audio/sound15.mp3",//投注开始前的倒计时3s音效

    BGM_VIDEO1 = "/audio/bgm_video1.mp3",//比赛开始背景音乐
    BGM_VIDEO2 = "/audio/bgm_video2.mp3",//比赛开始背景音乐
    BGM_VIDEO3 = "/audio/bgm_video3.mp3",//比赛开始背景音乐
    BGM_REWARD = "/audio/bgm_reward.mp3",//结算音效
    BGM_WAIT_BET = "/audio/bgm_wait_bet.mp3",//等待、下注期背景音乐
}

// 生成 sounds 数组
export const sounds: SoundConfig[] = Object.entries(SOUNDS).map(([key, src]) => {
    const isBgm = key.startsWith('BGM_');
    return {
        id: key,
        src,
        loop: isBgm, // 背景音乐循环播放
        poolSize: 1, // 背景音乐单实例，音效支持3个并发
    };
});

// 特殊投注：龙虎、大小、单双
export enum SpecialBet {
    DRAGON = "DRAGON",
    BIG = "1ST BIG",
    SINGULAR = "1ST Singular",
    EVEN = "1ST Even",
    SMALL = "1ST Small",
    TIGER = "TIGER",
}
