syntax = "proto3";

package socket;

// WebSocket响应数据结构
message SocketResData {
  string msgId = 1;
  bytes data = 2;  // 加密后的数据
}

// WebSocket消息数据结构
message SocketData {
  int64 currentTimeMillis = 1;
  string msgId = 2;
  string path = 3;
  string serverName = 4;
  google.protobuf.Any data = 5;  // 任意类型的数据
}

// 用户投注数据
message UserBetData {
  string playTypeId = 1;
  double amount = 2;
  string roundId = 3;
}

// 用户撤销数据
message UserRevokeData {
  string playTypeId = 1;
  string roundId = 2;
}

// 游戏配置数据
message GameConfigData {
  repeated BetChip betChips = 1;
  VideoStreamList videoStreams = 2;
  repeated GameType gameTypes = 3;
}

// 投注筹码
message BetChip {
  double value = 1;
}

// 视频流列表
message VideoStreamList {
  VideoBlock domestic = 1;
  VideoBlock international = 2;
}

// 视频块
message VideoBlock {
  map<int32, VideoStreamPair> streams = 1;
}

// 视频流对
message VideoStreamPair {
  VideoStream a = 1;
  VideoStream b = 2;
}

// 视频流
message VideoStream {
  string title = 1;
  string url = 2;
  int32 resolution = 3;
}

// 游戏类型
message GameType {
  string playTypeId = 1;
  string playTypeName = 2;
  string betTitle = 3;
  double rate = 4;
  int32 sort = 5;
  bool status = 6;
}

// 用户余额数据
message UserBalanceData {
  double balance = 1;
  string currency = 2;
  string userId = 3;
}

// 游戏历史数据
message GameHistoryData {
  repeated HistoryTrend trends = 1;
  repeated HistoryResult results = 2;
}

// 历史趋势
message HistoryTrend {
  string roundId = 1;
  string result = 2;
  bool even = 3;
  string num = 4;
}

// 历史结果
message HistoryResult {
  string roundId = 1;
  string result = 2;
  int64 timestamp = 3;
}

// 投注金额数据
message BetAmountData {
  map<string, double> amounts = 1;
}

// 游戏状态数据
message GameStatusData {
  string status = 1;
  int64 timestamp = 2;
  int32 countdown = 3;
  string roundId = 4;
}

// 中奖通知数据
message PrizeNoticeData {
  string userId = 1;
  string userName = 2;
  double amount = 3;
  string playType = 4;
}

// 心跳数据
message PingPongData {
  int64 timestamp = 1;
}

// 导入Google的Any类型
import "google/protobuf/any.proto";
