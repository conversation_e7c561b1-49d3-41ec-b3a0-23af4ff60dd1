<script setup lang="ts">
import {ref} from "vue";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore, useUserStore} from "@/stores";
import {useWebSocket} from "@/composables/useWebSocket.ts";
import {useDisableZoom} from "@/composables/useDisableZoom.ts";
import {useVolume} from "@/composables/useVolume.ts";
import {SocketEventCode} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter.ts";

import MaintainInfo from "@/components/MaintainInfo/index.vue";
import InitScene from "@/components/InitScene/index.vue";
import CanvasLayout from "@/layout/CanvasLayout.vue";
import MobileLayout from "@/layout/MobileLayout.vue";
import DesktopLayout from "@/layout/DesktopLayout.vue";

const { token } = storeToRefs(useUserStore());
const { isHorizontal } = storeToRefs(useMainStore());
const { setBalance, setCurrency, setUserId } = useUserStore();
const {
  updateGameInfo,
  setVideoStreamList,
  setBetChipsList,
  setGamePlayType,
  setHistoryBetResult,
  setHistoryBallRate,
  setHistoryTrend,
  cleanGameAwardAmount,
} = useGameStore();

const loading = ref(true);

const isMaintain = ref(false);

const setupSocketEvent = () => {
  const { connect, subscribe, send } = useWebSocket(
    `${import.meta.env.VITE_SOCKET_URL}/websocket?token=${token.value}`,
    () => {
      emitter.emit('get-game-config-req');
      emitter.emit('get-user-balance-req');
      emitter.emit("get-game-history-req");
      emitter.emit("get-game-history-bet-req");
    }
  );
  // 订阅socket事件 通过emitter分发出去
  subscribe(SocketEventCode.SOCKET_INIT, data => emitter.emit('socket-init', data));
  subscribe(SocketEventCode.WAIT, data => emitter.emit('game-wait', data));
  subscribe(SocketEventCode.BET, data => emitter.emit('game-bet', data));
  subscribe(SocketEventCode.BET_END, data => emitter.emit('game-bet-end', data));
  subscribe(SocketEventCode.GAME_START, data => emitter.emit('game-start', data));
  subscribe(SocketEventCode.GAME_END, data => emitter.emit('game-end', data));
  subscribe(SocketEventCode.SETTLEMENT, data => emitter.emit('game-settlement', data));
  subscribe(SocketEventCode.NOTICE_ONE_PRIZE, data => emitter.emit('game-award', data));
  subscribe(SocketEventCode.BET_AMOUNT, data => emitter.emit('game-bet-amount', data));

  subscribe(SocketEventCode.GAME_PAUSED, data => emitter.emit('game-paused', data));
  subscribe(SocketEventCode.USER_BET, data => emitter.emit('user-bet-res', data));
  subscribe(SocketEventCode.USER_REVOKE, data => emitter.emit('user-revoke-res', data));
  subscribe(SocketEventCode.GET_GAME_CONFIG, data => emitter.emit('get-game-config-res', data));
  subscribe(SocketEventCode.GET_USER_BALANCE, data => emitter.emit('get-user-balance-res', data));
  subscribe(SocketEventCode.GET_GAME_HISTORY, data => emitter.emit('get-game-history-res', data));
  subscribe(SocketEventCode.GET_GAME_HISTORY_BET, data => emitter.emit('get-game-history-bet-res', data));
  subscribe(SocketEventCode.GET_PLAYER_BET, data => emitter.emit('get-player-bet-res', data));
  subscribe(SocketEventCode.GET_GAME_RULES, data => emitter.emit('get-game-rules-res', data));

  // 游戏配置请求
  emitter.on('get-game-config-req', () => {
    send(SocketEventCode.GET_GAME_CONFIG);
  });
  // 游戏配置返回
  emitter.on('get-game-config-res', ({ data: res }: SocketData) => {
    const { code, data } = res || {};
    if (code === 200) {
      setVideoStreamList(data.playUrl);
      setBetChipsList(data.gameBetConfig);
      setGamePlayType(data.typeList);
    }
  });

  // 用户余额请求
  emitter.on('get-user-balance-req', () => {
    send(SocketEventCode.GET_USER_BALANCE);
  });
  // 用户余额返回
  emitter.on('get-user-balance-res', ({ data: res }: SocketData) => {
    const { code, data } = res || {};
    if (code === 200) {
      setBalance(data?.balance);
      setCurrency(data?.currency);
      setUserId(data?.userId);
    }
  });

  // 投注请求
  emitter.on('user-bet-req', (data: any) => {
    send(SocketEventCode.USER_BET, data);
  });
  // 投注返回
  emitter.on('user-bet-res', (res: SocketData) => { });

  // 撤销请求
  emitter.on('user-revoke-req', (data: any) => {
    send(SocketEventCode.USER_REVOKE, data);
  });
  // 撤销返回
  emitter.on('user-revoke-res', (res: SocketData) => { });

  //获取游戏历史结果请求
  emitter.on('get-game-history-req', () => {
    send(SocketEventCode.GET_GAME_HISTORY);
  });
  //获取游戏历史结果返回
  emitter.on('get-game-history-res', (res: SocketData) => {
    const { data, code } = res.data || {};
    if (code === 200) {
      setHistoryBallRate(data?.rateMap ?? {});
      setHistoryTrend(data?.historyResultVOList ?? []);
    }
  });

  //获取游戏历史下注记录请求
  emitter.on('get-game-history-bet-req', () => {
    send(SocketEventCode.GET_GAME_HISTORY_BET);
  });
  //获取游戏历史下注记录返回
  emitter.on('get-game-history-bet-res', (res: SocketData) => {
    if (res?.data?.code === 200) {
      setHistoryBetResult(res.data.data);
    }
  });

  // 获取当前玩家投注结果请求
  emitter.on('get-player-bet-req', (res: any) => {
    send(SocketEventCode.GET_PLAYER_BET, res);
  });
  // 获取当前玩家投注结果返回
  emitter.on("get-player-bet-res", (res: SocketData) => {});

  // 获取游戏规则请求
  emitter.on('get-game-rules-req', (data: any) => {
    send(SocketEventCode.GET_GAME_RULES, data)
  });
  // 获取游戏规则返回
  emitter.on('get-game-rules-res', (res: SocketData) => {
    updateGameInfo(res);
  });

  // socket首次进入返回当前游戏状态
  emitter.on('socket-init', (res: SocketData) => {
    const status = res.data?.data?.state?.toString();
    switch (status) {
      case SocketEventCode.GAME_PAUSED:
        emitter.emit('game-paused', { ...res, path: status });
        break;
      case SocketEventCode.WAIT:
        emitter.emit('game-wait', { ...res, path: status });
        break;
      case SocketEventCode.BET:
        emitter.emit('game-bet', { ...res, path: status });
        break;
      case SocketEventCode.BET_END:
        emitter.emit('game-end', { ...res, path: status });
        break;
      case SocketEventCode.GAME_START:
        emitter.emit('game-start', { ...res, path: status });
        break;
      case SocketEventCode.GAME_END:
        emitter.emit('game-end', { ...res, path: status });
        break;
      case SocketEventCode.SETTLEMENT:
        emitter.emit('game-settlement', { ...res, path: status });
        break;
    }
  });
  // 等待游戏开始投注
  emitter.on('game-wait', (res: SocketData) => {
    isMaintain.value = false;
    updateGameInfo(res)
    cleanGameAwardAmount()
    emitter.emit('get-user-balance-req');
    emitter.emit('show-switch-scenes');
  });
  // 游戏开始下注
  emitter.on('game-bet', (res: SocketData) => {
    updateGameInfo(res);
  });
  // 游戏下注结束
  emitter.on('game-bet-end', (res: SocketData) => {
    updateGameInfo(res);
    emitter.emit('show-switch-scenes');
  });
  // 游戏开始
  emitter.on('game-start', (res: SocketData) => {
    updateGameInfo(res);
    emitter.emit('play-sound', 'SOUND_14');
  });
  // 游戏结束
  emitter.on('game-end', (res: SocketData) => {
    updateGameInfo(res);
  });
  // 游戏结算
  emitter.on('game-settlement', (res: SocketData) => {
    updateGameInfo(res);
  });
  // 游戏其他玩家下注金额
  emitter.on('game-bet-amount', (res: SocketData) => { });
  // 中奖推送
  emitter.on('game-award', (res: SocketData) => {
    updateGameInfo(res);
  });
  // 游戏维护
  emitter.on('game-paused', (res: SocketData) => {
    if (res.data?.code === 200) {
      isMaintain.value = true;
    }
  });

  connect();
}

const setupVolume = () => {
  const { playSound } = useVolume();

  // 音效事件监听
  emitter.on('play-sound', (key: string) => playSound(key));
}

const handleFinish = () => {
  loading.value = false;
}

useDisableZoom();

setupVolume();

setupSocketEvent();
</script>

<template>
  <CanvasLayout>
    <div id="view-box">
      <InitScene v-if="loading" @finish="handleFinish" />

      <Component v-else :is="isHorizontal ? DesktopLayout : MobileLayout" />

      <MaintainInfo v-if="isMaintain" />
    </div>
  </CanvasLayout>
</template>

<style scoped>
#view-box {
  width: 100%;
  height: 100%;
  position: relative;
  overscroll-behavior: none;
  overflow: hidden;
}
</style>