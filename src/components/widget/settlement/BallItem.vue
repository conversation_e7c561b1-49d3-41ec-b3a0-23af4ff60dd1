<script setup lang="ts">
const props = defineProps<{
  ball: string; // 当前球号码
  order: number; // 当前球排名
}>();
</script>

<template>
  <div class="relative flex flex-col items-center">
    <img :src="`/images/ball-${props.ball}.png`" :alt="`ball ${props.ball}`" class="w-4 layout-y:w-[24.5px]"/>
    <img src="/images/ball-bg.png" alt="ball order bg" class="layout-x:w-[22px] layout-x:mt-[-9px] layout-y:w-[28px] layout-y:mt-[-11px]"/>
    <span class="ball-order mt-1 text-white text-default layout-x:top-[15px] layout-y:top-[27px]">{{ props.order }}</span>
  </div>
</template>

<style scoped>
.ball-order {
  position: absolute;
  font-weight: 500;
}
</style>