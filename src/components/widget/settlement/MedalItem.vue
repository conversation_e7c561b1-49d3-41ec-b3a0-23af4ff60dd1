<script setup lang="ts">
import { computed } from "vue";

const MEDAL_MAP = [ "gold", "sliver", "bronze" ];
const props = defineProps<{
  ball: string, // 当前球号码
  order: number // 当前球排名
}>();
const medal = computed(() => MEDAL_MAP[props.order - 1]);
const isGold = computed(() => props.order === 1);
</script>Ò

<template>
  <div class="relative">
    <img :src="`/images/medal-${medal}.png`" :alt="medal" class="medal layout-x:w-[19px] layout-y:w-[41px]">
    <img v-if="isGold" src="@/assets/svg/medal-gold-bg.svg" :alt="`${medal} bg`" class="layout-y:w-[102px]">
    <img v-else-if="props.order === 2" src="@/assets/svg/medal-sliver-bg.svg" :alt="`${medal} bg`" class="layout-y:w-[78px]">
    <img v-else-if="props.order === 3" src="@/assets/svg/medal-bronze-bg.svg" :alt="`${medal} bg`" class="layout-y:w-[78px]">
    <img src="@/assets/svg/ball-halo.svg" :alt="`ball halo ${medal}`" :class="[isGold ? 'top-[32%] layout-x:w-[34px] layout-y:w-[50px]' : 'top-[19%] layout-x:w-[28px] layout-y:w-[38px]']" class="ball-halo"/>
    <img :src="`/images/ball-${ball}.png`" :alt="`ball ${medal}`" :class="[isGold ? 'top-[40%] layout-x:w-[24px] layout-y:w-[36px]' : 'top-[25%] layout-x:w-[20px] layout-y:w-[26px]']" class="ball"/>
  </div>
</template>

<style scoped>
.medal {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -60%);
}

.ball-halo {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.ball {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
</style>