<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useMainStore } from "@/stores";
import { CameraType } from "@/constant/gameData";

const { cameraInfo } = storeToRefs(useMainStore());
const { toggleCameraType } = useMainStore()

const ActiveColor = "#B8001A";
const DefaultColor = "#5F0017";

const getColor = (type: CameraType) => {
  return cameraInfo.value.type === type ? ActiveColor : DefaultColor;
}

const handleClick = (target: CameraType) => {
  toggleCameraType(target);
};
</script>

<template>
  <div class="camera-list">
    <div class="camera-item prev cursor-pointer" @click="handleClick(CameraType.A)">
      <svg class="camera-bg" xmlns="http://www.w3.org/2000/svg" width="35" height="18" viewBox="0 0 35 18" fill="none">
        <path d="M0 0H35L26.9792 18H0V0Z" :fill="getColor(CameraType.A)"/>
      </svg>
      <div class="camera-inner">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="8" viewBox="0 0 14 8" fill="none">
          <path d="M12.6398 6.06402L12.6005 1.58496L9.54514 3.73212L12.6398 6.06402ZM12.0462 0.812394C12.1889 0.712118 12.3566 0.652462 12.5312 0.639912C12.7057 0.627362 12.8804 0.662397 13.0362 0.741208C13.1921 0.82002 13.3231 0.939593 13.415 1.08693C13.507 1.23427 13.5564 1.40373 13.5579 1.5769L13.5971 6.05597C13.5986 6.23311 13.5498 6.40711 13.4564 6.55822C13.3629 6.70932 13.2285 6.83147 13.0685 6.91079C12.9084 6.99011 12.7291 7.02343 12.5508 7.00697C12.3726 6.9905 12.2026 6.92491 12.0601 6.81764L8.96499 4.48574C8.84554 4.39572 8.74916 4.27916 8.68363 4.14548C8.61811 4.0118 8.58528 3.86474 8.5878 3.71618C8.59031 3.56762 8.6281 3.42173 8.69812 3.2903C8.76813 3.15886 8.86841 3.04556 8.99084 2.95956L12.0462 0.812394Z" fill="white" fill-opacity="0.9"/>
          <path d="M1.37759 0.803274C1.2558 0.803274 1.139 0.84559 1.05289 0.920911C0.966771 0.996233 0.918392 1.09839 0.918392 1.20491V6.44346C0.918392 6.54999 0.966771 6.65214 1.05289 6.72747C1.139 6.80279 1.2558 6.8451 1.37759 6.8451H7.92572C8.04751 6.8451 8.16431 6.80279 8.25042 6.72747C8.33654 6.65214 8.38492 6.54999 8.38492 6.44346V1.20491C8.38492 1.09839 8.33654 0.996233 8.25042 0.920911C8.16431 0.84559 8.04751 0.803274 7.92572 0.803274H1.37759ZM1.37759 0H7.92572C8.29108 0 8.64148 0.126946 8.89983 0.35291C9.15817 0.578875 9.30331 0.885349 9.30331 1.20491V6.44346C9.30331 6.76303 9.15817 7.0695 8.89983 7.29547C8.64148 7.52143 8.29108 7.64838 7.92572 7.64838H1.37759C1.01223 7.64838 0.661834 7.52143 0.403486 7.29547C0.145138 7.0695 0 6.76303 0 6.44346V1.20491C0 0.885349 0.145138 0.578875 0.403486 0.35291C0.661834 0.126946 1.01223 0 1.37759 0Z" fill="white" fill-opacity="0.9"/>
        </svg>
        <span>1</span>
      </div>
    </div>
    <div class="camera-item next cursor-pointer" @click="handleClick(CameraType.B)">
      <svg class="camera-bg" xmlns="http://www.w3.org/2000/svg" width="36" height="18" viewBox="0 0 36 18" fill="none">
        <path d="M36 18H0L7.92 0H36V18Z" :fill="getColor(CameraType.B)"/>
      </svg>
      <div class="camera-inner">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="8" viewBox="0 0 14 8" fill="none">
          <path d="M12.6398 6.06402L12.6005 1.58496L9.54514 3.73212L12.6398 6.06402ZM12.0462 0.812394C12.1889 0.712118 12.3566 0.652462 12.5312 0.639912C12.7057 0.627362 12.8804 0.662397 13.0362 0.741208C13.1921 0.82002 13.3231 0.939593 13.415 1.08693C13.507 1.23427 13.5564 1.40373 13.5579 1.5769L13.5971 6.05597C13.5986 6.23311 13.5498 6.40711 13.4564 6.55822C13.3629 6.70932 13.2285 6.83147 13.0685 6.91079C12.9084 6.99011 12.7291 7.02343 12.5508 7.00697C12.3726 6.9905 12.2026 6.92491 12.0601 6.81764L8.96499 4.48574C8.84554 4.39572 8.74916 4.27916 8.68363 4.14548C8.61811 4.0118 8.58528 3.86474 8.5878 3.71618C8.59031 3.56762 8.6281 3.42173 8.69812 3.2903C8.76813 3.15886 8.86841 3.04556 8.99084 2.95956L12.0462 0.812394Z" fill="white" fill-opacity="0.9"/>
          <path d="M1.37759 0.803274C1.2558 0.803274 1.139 0.84559 1.05289 0.920911C0.966771 0.996233 0.918392 1.09839 0.918392 1.20491V6.44346C0.918392 6.54999 0.966771 6.65214 1.05289 6.72747C1.139 6.80279 1.2558 6.8451 1.37759 6.8451H7.92572C8.04751 6.8451 8.16431 6.80279 8.25042 6.72747C8.33654 6.65214 8.38492 6.54999 8.38492 6.44346V1.20491C8.38492 1.09839 8.33654 0.996233 8.25042 0.920911C8.16431 0.84559 8.04751 0.803274 7.92572 0.803274H1.37759ZM1.37759 0H7.92572C8.29108 0 8.64148 0.126946 8.89983 0.35291C9.15817 0.578875 9.30331 0.885349 9.30331 1.20491V6.44346C9.30331 6.76303 9.15817 7.0695 8.89983 7.29547C8.64148 7.52143 8.29108 7.64838 7.92572 7.64838H1.37759C1.01223 7.64838 0.661834 7.52143 0.403486 7.29547C0.145138 7.0695 0 6.76303 0 6.44346V1.20491C0 0.885349 0.145138 0.578875 0.403486 0.35291C0.661834 0.126946 1.01223 0 1.37759 0Z" fill="white" fill-opacity="0.9"/>
        </svg>
        <span>2</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.camera-list {
  height: 18px;
  display: flex;
  align-items: center;
}

.camera-list .camera-item {
  position: relative;
  height: 100%;
}

.camera-list .camera-item:first-child .camera-inner {
  padding-left: 5px;
  justify-content: start;
}

.camera-list .camera-item:last-child .camera-inner {
  padding-right: 5px;
  justify-content: end;
}

.camera-list .camera-item .camera-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1px;
  color: white;
  font-size: 10px;
  line-height: 14px;
  font-weight: 400;
  position: absolute;
  inset: 0;
  margin: auto;
}

.camera-list .camera-item .camera-inner span {
  transform: translateY(-1px);
}
</style>