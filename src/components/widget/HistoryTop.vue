<template>
  <div id="top" class="relative pl-[30px] pr-[30px]" style="height: calc(100% - 22px)">
    <swiper-container
      class="h-full"
      :navigation="{
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      }"
      :pagination="{ el: '.swiper-pagination' }"
      :spaceBetween="30"
      @swiper="onSwiper"
      ref="swiperRef"
    >
      <swiper-slide
        v-for="(slideItems, index) in groupedLists"
        :key="index"
        ref="slideRefs"
      >
        <section class="grid grid-flow-col gap-x-9 flex-wrap" :class="delayedGridRows">
          <div v-for="item in slideItems" :key="item.id" ref="itemRefs">
            <p
              :style="`margin-top: ${!hBetPanelShow ? 12 : 6}px`"
              class="text-white"
            >
              NO.{{ item.roundId }}
            </p>
            <div class="flex align-center justify-between w-[178px] mt-1">
              <img
                v-for="num in item.result.split(',')"
                :key="num"
                :src="`/images/ball-${num}.png`"
                alt=""
                class="w-4 h-4"
              />
            </div>
          </div>
        </section>
      </swiper-slide>
    </swiper-container>
    <div class="swiper-pagination h-[20px]"></div>
    <div
      class="swiper-button-prev absolute top-[42%] left-[5px]"
      @click="swiper && swiper.slidePrev()"
    >
      <div class="switch-btn">
        <img src="/images/arrow-left.png" alt="arrow left" class="block size-full" />
      </div>
    </div>
    <div
      class="swiper-button-next absolute top-[42%] right-[5px]"
      @click="swiper && swiper.slideNext()"
    >
      <div class="switch-btn">
        <img src="/images/arrow-right.png" alt="arrow right" class="block size-full" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, defineProps } from "vue";
import { storeToRefs } from "pinia";
import { useMainStore } from "@/stores";

const { hBetPanelShow } = storeToRefs(useMainStore());
const swiperRef = ref<any>(null);

// 历史期数数据
const lists = ref<any[]>([]);

const slideRefs = ref<any[]>([]);
const itemRefs = ref<any[]>([]);

// 原始分组列表
const rawGroupedLists = computed(() => {
  const groups = [];
  let index = 0;
  // 根据 hBetPanelShow 的值确定每个 slide 容纳的数量
  const itemsPerSlide = hBetPanelShow.value ? 6 : 15;
  while (index < lists.value.length) {
    groups.push(lists.value.slice(index, index + itemsPerSlide));
    index += itemsPerSlide;
  }
  return groups;
});

// 用于渲染的分组列表
const groupedLists = ref(rawGroupedLists.value);

// 延迟时间（毫秒）
const DELAY_TIME = 500;
// 存储延迟后的 grid-rows 类名
const delayedGridRows = ref(hBetPanelShow.value ? 'grid-rows-2' : 'grid-rows-5');

const props = defineProps(
  {
    historyLists: {
      type: Array,
      default: () => [],
    }
  }
)

// 监听 hBetPanelShow 的变化
watch(hBetPanelShow, async (newValue) => {
  if (swiperRef.value) {
    if (newValue) {
      // 延迟计算
      await new Promise(resolve => setTimeout(resolve, DELAY_TIME));
    }
    // 更新用于渲染的分组列表
    groupedLists.value = rawGroupedLists.value;
    swiperRef.value.swiper.slideTo(0);
  }
  setTimeout(() => {
    delayedGridRows.value = newValue? 'grid-rows-2' : 'grid-rows-5';
  });
});

// 监听传入的 historyLists
watch(
  () => props.historyLists,
  (newHistoryLists) => {
    lists.value = newHistoryLists;
    groupedLists.value = rawGroupedLists.value;
  },
  { deep: true }
);

onMounted(async () => {
  groupedLists.value = rawGroupedLists.value;
});
</script>

<style scoped>
.switch-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 50%;
}

.swiper-pagination {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: -5px;
  left: 47%;
}
</style>
