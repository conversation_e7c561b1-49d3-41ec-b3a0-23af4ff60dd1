<template>
  <div class="ml-[39px] mr-[39px]">
    <table>
      <tbody>
        <tr v-for="i in lists" :key="i">
          <td v-for="(item, idx) in computedOddResult.slice(0, 28)" :key="`${item.even}_${item.count}_${idx}`">
            <template v-if="item.even">
              <img v-show="item.count >= i" src="/images/pc-even.png" alt="" class="w-4 h-4 ml-[2px] mr-[2px]">
            </template>
            <template v-else>
              <img v-show="item.count >= i" src="/images/pc-odd.png" alt="" class="w-4 h-4 ml-[2px] mr-[2px]">
            </template>
          </td>
        </tr>
        <tr v-show="hBetPanelShow">
          <td v-for="item in computedOddResult.slice(0, 28)" :key="item">
             {{ item.count > 3?`+${item.count - 3}`:'' }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, watch, onMounted, computed } from "vue";

const lists = ref(3);
const oddResult = ref([]);
const props = withDefaults(defineProps<{
  hBetPanelShow: boolean;
  historyLists: any[];
}>(), {
  historyLists: () => []
});

watch(props, (newValue) => {
  console.log(props);
  
  if (!newValue.hBetPanelShow) {
    lists.value = 12;
  } else {
    setTimeout(() => {
      lists.value = 3;
    }, 500);
  }
}, { deep: true });

// 处理单双数据的函数
const handleOdds = (data: Array<{ even: boolean }>) => {
  if (data.length === 0) return [];
  const result: Array<{id:number,even:boolean,count:number}> = [];
  let count = 0;
  for (let i = 0; i < data.length; i++) {
    if(result.length == 0){
      result.push({id:count,even:data[i].even,count:1});
    }else{
      if(result[result.length-1].even == data[i].even){
        result[result.length-1].count++;
      }else{
        count++;
        result.push({id:count,even:data[i].even,count:1});
      }
    }
  }
  return result;
};

// 使用计算属性监听
const computedOddResult = computed(() => {
  return handleOdds(props.historyLists);
});

onMounted(() => {
  if (props.hBetPanelShow) {
    lists.value = 3;
  } else {
    lists.value = 12;
  }
});
</script>

<style scoped>
table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.3); /* 添加表格边框 */
}

tr {
  border: 1px solid rgba(255, 255, 255, 0.3); /* 添加行边框 */
  height: 18px;
}
td img{
  width: 16px !important;
  height: 16px!important;
}

td {
  width: 18px;
  padding: 0 !important;
  height: auto !important;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3); /* 添加单元格边框 */
  line-height: 18px;
  color: #ffffff;
  font-size: 10px;
  font-weight: 700;
}
</style>
