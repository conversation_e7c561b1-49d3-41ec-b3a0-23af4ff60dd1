<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { storeToRefs } from "pinia";
  import { useGameStore, useMainStore } from "@/stores";
  import { openModal } from "@/components/ui/GModal/index.ts";

  import SettingModal from "@/components/modal/setting/index.vue";
  import RulesModal from "@/components/modal/rules/index.vue";

  const { cameraInfo } = storeToRefs(useMainStore());
  const { videoQualityList } = storeToRefs(useGameStore());
  const { setCameraQuality, toggleIsRotation } = useMainStore()

  type ToolBarType = "quality" | "setting" | "rate" | "rule";

  withDefaults(defineProps<{
    toolbar?: ToolBarType[];
    background?: boolean;
    size?: number;
  }>(), {
    toolbar: () => ["quality", "setting", "rate"],
    background: false,
    size: 20,
  })

  //显示视频选择
  const isShowQuality = ref(false)

  const showSettingModal = () => {
    openModal(SettingModal);
  }

  const showRulesModal = () => {
    openModal(RulesModal);
  }

  //设置视频清晰度
  const handleQuality = (value: number) => {
    setCameraQuality(value)
    isShowQuality.value = false
  }

  const oinDomClick = (e: any) => {
    const classList = Array.from(e.target.classList)
    if (!classList.includes('quality-btn--horizontal') && !classList.includes('quality-btn--vertical')) {
      isShowQuality.value = false
    }
  }

  onMounted(() => {
    window.addEventListener('click', oinDomClick)
  })

  onUnmounted(() => {
    window.removeEventListener('click', oinDomClick)
  })
</script>
 
<template>
  <div class="toolbar-btn-widget layout-x:gap-4 layout-y:gap-5">
    <template v-if="toolbar.includes('quality')">
      <div class="relative layout-y:hidden">
        <button class="quality-btn--horizontal cursor-pointer" @click="isShowQuality=!isShowQuality">{{ cameraInfo.quality }}p</button>
        <div class="quality-list absolute left-0 top-[18px] z-99" v-show="isShowQuality">
          <div
            v-for="item of videoQualityList"
            :key="item"
            :class="[
              `quality-item`,
              'cursor-pointer',
              item === cameraInfo.quality ? 'option-selected' : ''
            ]"
            @click="handleQuality(item)"
          >
            {{ item }}p
          </div>
        </div>
      </div>
      <div class="relative layout-x:hidden ">
        <button class="quality-btn--vertical mr-auto" @click="isShowQuality=!isShowQuality">{{ cameraInfo.quality }}p</button>
        <div class="quality-list absolute left-0 bottom-full z-99" v-show="isShowQuality">
          <div
            v-for="item of videoQualityList"
            :key="item"
            :class="[
              `quality-item`,
              item === cameraInfo.quality ? 'option-selected' : ''
            ]"
            @click="handleQuality(item)"
          >
            {{ item }}p
          </div>
        </div>
      </div>
    </template>

    <button v-if="toolbar.includes('setting')" class="setting-btn btn-icon cursor-pointer layout-y:ml-auto" :class="{ bg: background }" @click="showSettingModal">
      <img src="/images/setting-icon.png" alt="setting" class="w-5 max-w-full block m-auto" />
    </button>

    <button v-if="toolbar.includes('rate')" class="rate-btn btn-icon cursor-pointer" :class="{ bg: background }" @click="toggleIsRotation()">
      <img src="/images/rate-icon.png" alt="rate" class="w-4 max-w-full block h-auto m-auto" />
    </button>

    <button v-if="toolbar.includes('rule')" class="rule-btn btn-icon cursor-pointer" :class="{ bg: background }" @click="showRulesModal">
      <img src="/images/rules-icon.png" alt="rules" class="w-5 max-w-full block m-auto" />
    </button>
  </div>
</template>

<style lang="scss" scoped>
.toolbar-btn-widget {
  display: flex;
  align-items: center;
}

.quality-btn--horizontal {
  width: 42px;
  height: 18px;
  border: 1px solid white;
  border-radius: 2px;
  color: white;
}

.quality-list {
  width: 100%;
  height: fit-content;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
  color: white;
  padding: 4px 0;
}

.quality-item {
  text-align: center;
  padding: 4px 0;
}

.option-selected{
  color: #ff003f;
}

.quality-btn--vertical {
  height: 28px;
  padding-inline: 8px;
  border-radius: 2px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.5);
  font-size: 10px;
  line-height: 28px;
  color: white;
}

.btn-icon {
  overflow: hidden;
  color: white;
  width: calc(v-bind(size) * 1px);
  height: calc(v-bind(size) * 1px);

  &.bg {
    border-radius: 50%;
    color: #E7E7E8;
    background-color: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}
</style>