<script setup lang="ts">
import {onMounted, onUnmounted, watch} from "vue";
import {storeToRefs} from "pinia";
import {animate, createTimer, utils} from "animejs";
import {useGameStore, useMainStore} from "@/stores";
import {GameStatus} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter.ts";

const { isVertical } = storeToRefs(useMainStore());
const { gameBetEndTime, gameNextWaitEndTime, gameStatus } = storeToRefs(useGameStore());
const { cleanGameBetEndTime, cleanGameNextWaitEndTime } = useGameStore();

const betText = 'Betting time';
const nextText = 'Countdown to the next round';

let cleanFn: (() => void) | void;

const getTimeText = () => {
  const d = new Date();
  const pad2 = utils.padStart(2, '0');
  const pad3 = utils.padStart(3, '0');
  const h = pad2(d.getHours());
  const m = pad2(d.getMinutes());
  const s = pad2(d.getSeconds());
  const ms = pad3(d.getMilliseconds());
  return `${h}:${m}:${s}.${ms}`;
}

const setupCountDownTl = (endTime: number, isBetCountDown: boolean) => {
  cleanFn?.();
  const [ $titleRef ] = utils.$('.title-el');
  const [ $descRef ] = utils.$('.desc-el');
  if (!$titleRef || !$descRef) return console.error('未找到元素');
  const a1 = isVertical.value
        ? animate($titleRef, { y: 34, fontSize: 10, duration: 300 })
        : animate($titleRef, { y: 50, fontSize: 14, duration: 300 });
  const a2 = isVertical.value
        ? animate($descRef, { y: 76, opacity: 1, duration: 300 })
        : animate($descRef, { y: 90, opacity: 0, duration: 300 });
  const a3 = createTimer({
    frameRate: 1,
    onUpdate: () => {
      const time = Math.ceil(Math.max(0, endTime - Date.now()) / 1000);

      $titleRef.textContent = `${ !isVertical.value ? betText + ' ' : '' }${time}s`;

      if (time === 3 && isBetCountDown) {
        emitter.emit('play-bet-prompt');
      }
      if (time <= 3) {
        emitter.emit('play-sound', 'SOUND_15');
      }
    }
  });
  $descRef.textContent = isBetCountDown ? betText : nextText;

  return () => {
    a1.pause();
    a2.pause();
    a3.pause();
  }
}

const setupNowTimeTl = () => {
  cleanFn?.();
  const [ $titleRef ] = utils.$('.title-el');
  const [ $descRef ] = utils.$('.desc-el');
  const a1 = animate($titleRef, { y: 50, fontSize: 14, duration: 300 });
  const a2 = animate($descRef, { y: 90, opacity: 0, duration: 300 });
  const a3 = createTimer({
    duration: 1000,
    frameRate: 8,
    loop: true,
    onUpdate: () => {
      if ($titleRef) {
        $titleRef.textContent = getTimeText();
      }
    },
  });
  return () => {
    a1.pause()
    a2.pause()
    a3.pause()
  }
}

const init = () => {
  if (gameBetEndTime.value && gameStatus.value === GameStatus.GAME_BET) {
    cleanFn = setupCountDownTl(gameBetEndTime.value, true);
    return;
  }

  if (gameNextWaitEndTime.value && gameStatus.value === GameStatus.GAME_SETTLEMENT) {
    cleanFn = setupCountDownTl(gameNextWaitEndTime.value, false);
    return;
  }

  cleanFn = setupNowTimeTl();
}

onMounted(() => {
  init()

  watch(gameStatus, (status) => {
    if (status !== GameStatus.GAME_BET) {
      cleanGameBetEndTime();
    }
    if (status !== GameStatus.GAME_SETTLEMENT) {
      cleanGameNextWaitEndTime();
    }
    init();
  });
});

onUnmounted(() => {
  cleanFn?.();
});

</script>

<template>
  <svg class="layout-y:h-7.5 layout-x:h-6" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 195 25" fill="none">
    <path d="M148.428 -5.22949e-07L161.5 12.5L148.428 25L46.5727 25L34.0001 12.5L46.5727 -4.4609e-06L148.428 -5.22949e-07Z" fill="url(#paint0_linear_210_143)"/>
    <path d="M36.4855 2.86855e-06L24.618 12.5L36.4855 24.9999L27.994 24.9999L16.412 12.5L27.994 -3.39689e-07L36.4855 2.86855e-06Z" fill="url(#paint1_linear_210_143)" fill-opacity="0.8"/>
    <path d="M158.515 25L170.382 12.5L158.515 6.86646e-05L167.006 6.97777e-05L178.588 12.5L167.006 25L158.515 25Z" fill="url(#paint2_linear_210_143)" fill-opacity="0.8"/>
    <path class="layout-y:hidden" d="M20.0735 2.86855e-06L8.20606 12.5L20.0735 24.9999L11.582 24.9999L1.84765e-05 12.5L11.582 -3.39689e-07L20.0735 2.86855e-06Z" fill="url(#paint3_linear_210_143)" fill-opacity="0.4"/>
    <path class="layout-y:hidden" d="M174.927 25L186.794 12.5L174.927 6.86646e-05L183.418 6.97777e-05L195 12.5L183.418 25L174.927 25Z" fill="url(#paint4_linear_210_143)" fill-opacity="0.4"/>
    <text class="title-el font-bold text-white" fill="#fff" x="50%" y="34%" text-anchor="middle" dominant-baseline="middle">30s</text>
    <text class="desc-el" fill="#fff" x="50%" y="76%" text-anchor="middle" dominant-baseline="middle">Betting time</text>
    <defs>
      <linearGradient id="paint0_linear_210_143" x1="48.2355" y1="12.5" x2="144.836" y2="12.5053" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#640018"/>
        <stop offset="0.355973" stop-color="#BD001B"/>
        <stop offset="0.604526" stop-color="#BD001B"/>
        <stop offset="1" stop-color="#5F0017"/>
      </linearGradient>
      <linearGradient id="paint1_linear_210_143" x1="36.4855" y1="12.5" x2="16.412" y2="12.5" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#BD001B"/>
        <stop offset="1" stop-color="#5E0017"/>
      </linearGradient>
      <linearGradient id="paint2_linear_210_143" x1="158.515" y1="12.5" x2="178.588" y2="12.5" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#BD001B"/>
        <stop offset="1" stop-color="#5E0017"/>
      </linearGradient>
      <linearGradient id="paint3_linear_210_143" x1="20.0735" y1="12.5" x2="1.84765e-05" y2="12.5" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#BD001B"/>
        <stop offset="1" stop-color="#5E0017"/>
      </linearGradient>
      <linearGradient id="paint4_linear_210_143" x1="174.927" y1="12.5" x2="195" y2="12.5" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#BD001B"/>
        <stop offset="1" stop-color="#5E0017"/>
      </linearGradient>
    </defs>
  </svg>
</template>

<style scoped>

</style>