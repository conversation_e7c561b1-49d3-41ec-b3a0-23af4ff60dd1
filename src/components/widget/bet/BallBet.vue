<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { useGameStore, useMainStore, useUserStore } from "@/stores";
import { GameStatus } from "@/constant/gameData.ts";
import { openMessage } from "@/components/ui/GMessage/index.ts";
import emitter from "@/utils/emitter.ts";
import { getBoundingClientRect } from "@/utils/utils.ts";

import EffectWin from "@/components/widget/EffectWin.vue";

// 定义类型
interface BetResultType {
  code?: number;
  message?: string;
  // 可按需添加更多属性
}

interface SocketData {
  data?: {
    code?: number;
    data?: any;
  };
}

interface BetItem {
  location: number;
  amount: string;
  odds: string;
  image: string;
  bets: number;
  count: number;
}

// 从 store 中获取响应式数据
const { scale } = storeToRefs(useMainStore());
const {
  gameRoundId,
  gameId,
  gamePlayType,
  gameStatus,
  gameWinBet,
  betChipsResult,
  playerBetChipsResult,
} = storeToRefs(useGameStore());
const { setBetChipsResult, setPlayerBetChipsResult } = useGameStore();
const { balance } = storeToRefs(useUserStore());

const { t } = useI18n();
// 玩家下注结果
const betResult = ref<BetResultType>({});

// 下注列表
const lists = ref<BetItem[]>(Array.from({ length: 10 }, (_, index) => ({
  location: index + 1,
  amount: `1-${index + 1}`,
  odds: index === 9 ? "1:9" : "1:8",
  image: `/images/ball-${index + 1}.png`,
  bets: 0,
  count: 0,
})));

// 提取获取元素中心位置的函数
const getElementCenter = (element: HTMLElement) => {
  const {width,left,height,top} = getBoundingClientRect(element);
  return {
    x: (left + width / 2),
    y: (top + height / 2),
  };
};

// 下注提示
const handleBet = () => {
  openMessage({ message: t('no_bets_available_tips') });
};

// 处理下注并返回 Promise
const waitForBetResponse = () => {
  return new Promise<{ success: boolean; data?: any }>((resolve) => {
    const handler = (data: any) => {
      emitter.off("user-bet-res", handler);
      resolve({ success: data?.data?.code === 200, data });
    };
    emitter.on("user-bet-res", handler);
  });
};

// 处理撤销下注并返回 Promise
const waitForBackBetResponse = () => {
  return new Promise<{ success: boolean; data?: any }>((resolve) => {
    const handler = (data: any) => {
      emitter.off("user-revoke-res", handler);
      resolve({ success: data?.data?.data.flag, data: data.data.data });
    };
    emitter.on("user-revoke-res", handler);
  });
};

// 投放筹码
const takeBet = async (event: MouseEvent, index: number) => {
  if (gameStatus.value !== GameStatus.GAME_BET) {
    return handleBet();
  }

  const target = event.currentTarget as HTMLElement;
  const chip = document.querySelector(".chip-item.active") as HTMLElement;
  if (!chip) return;

  const count = Number(chip.getAttribute("data-value"));

  if(balance.value < count) {
    return;
  }

  emitter.emit("user-bet-req", {
    betAmount: count,
    gameId: gameId.value,
    roundId: gameRoundId.value,
    type: 1,
    location: lists.value[index].location,
  });

  const { success } = await waitForBetResponse();
  if (!success) return;

  emitter.emit('play-sound', 'SOUND_5');

  //复制筹码元素
  const chipClone = chip.cloneNode(true) as HTMLElement;
  chipClone.innerText = ''
  chipClone.style.cssText = `
    position: absolute;
    z-index: 9999;
    pointer-events: none;
    background-image: url(/images/bets-1.png);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  `;

  const viewContent = document.querySelector(".view-content");
  viewContent?.appendChild(chipClone);
  const chipRect =getBoundingClientRect(chip.children[0]);
  const viewContentRect = getBoundingClientRect(viewContent);
  chipClone.style.width = `${chipRect.width}px`;
  chipClone.style.height = `${chipRect.height}px`;
  if (viewContentRect) {
    chipClone.style.left = `${viewContentRect.width / 2 - chipRect.width / 2}px`;
  }
  chipClone.style.bottom = `${30 / scale.value}px`;
  const chipCloneCenter = getElementCenter(chipClone);
  const targetCenter = getElementCenter(target);
  const deltaX = targetCenter.x - chipCloneCenter.x;
  const deltaY = targetCenter.y - chipCloneCenter.y;

  chipClone.style.transition =
    "transform 0.5s ease-out, width 0.5s ease-out, height 0.5s ease-out";
  chipClone.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
  chipClone.style.width = "20px";
  chipClone.style.height = "20px";

  setTimeout(() => {
    chipClone.remove();
    lists.value[index].bets += 1;
    lists.value[index].count += count;
    setPlayerBetChipsResult({ recordLists: lists.value, roundId: gameRoundId.value });
    emitter.emit("get-user-balance-req");
  }, 500);
};

// 撤销下注
const backBets = async () => {
  emitter.emit("user-revoke-req", { roundId: gameRoundId.value });
  const { success } = await waitForBackBetResponse();
  if (!success) return;

  emitter.emit("get-user-balance-req");

  const chip = document.querySelector(".chip-item.active") as HTMLElement;
  const chipCenter = getElementCenter(chip);

  document.querySelectorAll(".ball-bets").forEach((item: HTMLElement) => {
    const itemCenter = getElementCenter(item);
    const deltaX = chipCenter.x - itemCenter.x;
    const deltaY = chipCenter.y - itemCenter.y;

    item.style.cssText = `
      transition: transform 0.5s ease-out;
      transform: translate(${deltaX}px, ${deltaY}px);
      position: fixed;
    `;
  });

  setTimeout(() => {
    lists.value.forEach((item) => {
      item.bets = 0;
      item.count = 0;
    });
    setPlayerBetChipsResult({});
  }, 500);
};

// 监听游戏玩法类型变化
watch(
  gamePlayType,
  (newValue) => {
    const rate = newValue?.find((item: any) => item.playTypeId === 1)?.rate[0];
    if (rate) {
      lists.value.forEach((item) => {
        item.odds = `1:${rate}`;
      });
    }
  },
  { deep: true }
);

// 监听游戏状态变化
watch(gameStatus, (newValue) => {
  if (newValue === "game-wait") {
    lists.value.forEach((item) => {
      item.bets = 0;
      item.count = 0;
    });
  }
});

// 转换数值为 1k, 2.5k, 1m 等格式的函数
const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    const value = num / 1000000000;
    return Number.isInteger(value) ? `${value}m` : `${value.toFixed(1).replace(/\.0$/, '')}B`;
  }
  if (num >= 100000) {
    const value = num / 100000;
    return Number.isInteger(value) ? `${value}m` : `${value.toFixed(1).replace(/\.0$/, '')}M`;
  }
  if (num >= 1000) {
    const value = num / 1000;
    return Number.isInteger(value) ? `${value}k` : `${value.toFixed(1).replace(/\.0$/, '')}K`;
  }
  return num.toString();
};

// 组件挂载时
onMounted(() => {
  emitter.on("back-ball-bets", backBets);
  if (Object.keys(betChipsResult.value).length) {
    betResult.value = { ...betChipsResult.value };
  } else {
    betResult.value = JSON.parse(localStorage.getItem("playerBetResult") || "{}").betChipsResult;
  }
  if (Object.keys(playerBetChipsResult.value).length && gameRoundId.value === playerBetChipsResult.value.roundId) {
    lists.value=[...playerBetChipsResult.value.recordLists];
  } else {
    try {
      const playerBetResult = JSON.parse(localStorage.getItem("playerBetResult") || "{}");
      const { playerBetChipsResult: playerbet, gameRoundId: roundId } =
        playerBetResult;
      if (roundId === playerbet?.roundId) {
        lists.value = [...playerbet.recordLists];
      }
    } catch (error) { }
  }
});

// 组件卸载时
onUnmounted(() => {
  emitter.off("back-bet-res");
  emitter.off("game-bet-amount");
  emitter.off("back-ball-bets");
  emitter.off("game-bet-res");
});

// 监听游戏下注金额事件
emitter.on("game-bet-amount", (data: SocketData) => {
  if (data?.data?.code === 200) {
    betResult.value = { ...data.data.data };
    setBetChipsResult({ ...data.data.data });
  }
});

// 监听游戏下注响应事件
emitter.on("game-bet-res", (data: any) => {
  if (data?.code !== 200) {
    backBets();
  }
});
</script>

<template>
  <div class="ball-bet-widget grid layout-x:grid-cols-10 layout-x:gap-[1px] layout-x:p-1 layout-y:grid-cols-5">
    <section v-for="(item, index) in lists" :key="index" class="ball-bet-item cursor-pointer"
      @click="takeBet($event, index)">
      <EffectWin v-if="index + 1 === gameWinBet.type1Loc" />
      <div class="bet-item--rate">
        <img :src="item.image" :alt="`ball ${index}`" />
        <div>{{ item.odds }}</div>
      </div>
      <div class="bet-item--chips">
        <template v-if="item.bets !== 0">
          <img :src="`/images/bets-${item.bets > 2 ? 3 : item.bets}.png`" :alt="`${index}-bets`"
            class="ball-bets h-[16px]" />
        </template>
      </div>
      <div class="bet-item--bets" v-if="gameStatus !== 'game-wait'">
        {{ formatNumber(item.count) }}/{{ formatNumber(betResult[item.amount] ?? 0) }}
      </div>
      <div class="bet-item--bets" v-else>bets</div>
    </section>
  </div>
</template>

<style scoped>
:where([data-layout="x"]) .ball-bet-widget {
  padding: 4px;
  gap: 1px;
}

:where([data-layout="y"]) .ball-bet-widget {
  padding: 6px 8px;
  gap: 6px 2px;
}

.ball-bet-widget {
  background: linear-gradient(90deg, #001458 10.47%, #7c0013 100%) center/calc(100% - 4px) calc(100% - 4px) no-repeat,
    linear-gradient(90deg, #002394 10.47%, #aa002a 100%) center/100% no-repeat;
}

.ball-bet-widget .ball-bet-item {
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-align: center;
}

.ball-bet-widget .ball-bet-item::after {
  opacity: 0;
  pointer-events: none;
  position: absolute;
  inset: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  transition: opacity 0.6s ease;
  transform: scale(1.046);
  content: "";
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url("/images/bet-light-border2.png");
}

.ball-bet-widget .ball-bet-item:hover::after {
  transition: none;
  opacity: 1;
}

.ball-bet-widget .ball-bet-item .bet-item--rate {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding-top: 6px;
  padding-bottom: 4px;
}

.ball-bet-widget .ball-bet-item .bet-item--rate img {
  display: block;
  width: 24px;
  height: 24px;
}

.ball-bet-widget .ball-bet-item .bet-item--rate div {
  font-size: 12px;
  font-weight: 700;
  color: white;
}

.ball-bet-widget .ball-bet-item .bet-item--chips {
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ball-bet-widget .ball-bet-item .bet-item--bets {
  padding-block: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
