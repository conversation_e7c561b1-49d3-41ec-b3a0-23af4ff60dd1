<script setup lang="ts">
import {storeToRefs} from "pinia";
import {onUnmounted, ref, watchEffect} from "vue";
import {useGameStore, useMainStore, useUserStore} from "@/stores";
import emitter from "@/utils/emitter.ts";
import {GameStatus} from "@/constant/gameData.ts";
import {getCountDownText} from "@/utils/utils.ts";
import {openModal} from "@/components/ui/GModal/index.ts";

import BetChips from "./BetChips.vue";
import RecordsModal from '@/components/modal/records/index.vue';

const { balance } = storeToRefs(useUserStore());
const { toggleHistoryShow } = useMainStore();
const { gameStatus, gameAwardAmount, gameNextWaitEndTime, betChipsList } = storeToRefs(useGameStore());

let timer: any;
let countdownText = ref('');

const showRecords = () => {
  openModal(RecordsModal, {});
};

// 定义调用 backBets 方法的函数
const callBackBets = () => {
  if (gameStatus.value === GameStatus.GAME_BET) {
    emitter.emit('back-tiger-bets');
    emitter.emit('back-ball-bets');
  }
};

const updateCountDownText = () => {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }

  const currTime = Date.now();

  if (gameNextWaitEndTime.value && gameNextWaitEndTime.value > currTime) {
    countdownText.value = getCountDownText(gameNextWaitEndTime.value - currTime)
    timer = setTimeout(updateCountDownText, 1000);
  } else {
    countdownText.value = "00:00:00";
  }
}

watchEffect(() => {
  if (gameStatus.value === GameStatus.GAME_SETTLEMENT && gameNextWaitEndTime.value) {
    updateCountDownText();
  }
});

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
    timer = null;
  }
});
</script>

<template>
  <section class="bet-bar-widget">
    <div class="btn-box left-box justify-start">
      <button class="btn-item records-btn layout-y:hidden! before:right-0 cursor-pointer" @click="showRecords">
        <img src="/images/records-icon.png" alt="records" class="block size-5 object-contain" />
        <span class="text-white/80 layout-x:text-xs layout-y:text-default">{{ $t('records') }}</span>
      </button>
      <button class="btn-item balance-btn layout-x:gap-1 layout-y:gap-2">
        <span class="w-full text-left text-white/80 pl-0.5 layout-x:text-xs layout-y:text-default">{{ $t('balance') }}</span>
        <span class="w-full text-left text-white/90 layout-x:text-sm layout-y:text-xs font-bold">{{ balance }}</span>
      </button>
    </div>

    <div ref="actionBoxRef" class="action-box">
      <Transition name="switch-status">
        <BetChips v-if="[GameStatus.GAME_WAIT, GameStatus.GAME_BET].includes(gameStatus as GameStatus) && betChipsList.length"
                 class="absolute"/>
        <div v-else-if="gameStatus === GameStatus.GAME_SETTLEMENT"
             class="absolute w-full flex flex-col justify-center items-center">
          <div class="layout-x:hidden font-bold flex flex-col items-center">
            <span class="text-[#22F34F] text-[20px]">+{{ gameAwardAmount }}</span>
            <span class="mt-1 text-[14px]">{{ $t('finally_make_a_profit') }}</span>
          </div>
          <div class="layout-y:hidden text-[12px] font-bold">
            {{ $t('finally_make_a_profit') }}：<span class="text-[#22F34F]">+{{ gameAwardAmount }}</span>
          </div>
          <div class="layout-y:hidden text-[#20CFFF] mt-[12px]">{{ $t('countdown_to_the_next_round') }} {{ countdownText }}</div>
        </div>
        <div v-else class="absolute w-full flex flex-col items-center">
          <img src="/images/warn-icon.png" alt="warn" class="w-[18px]"/>
          <div class="layout-x:mt-[6px] layout-x:text-[12px] flex flex-wrap justify-center">
            <span>{{ $t('the_game_is_in_progress') }},</span>
            <span>{{ $t('no_bets_available_now') }}!</span>
          </div>
        </div>
      </Transition>
    </div>

    <div class="btn-box right-box justify-end">
      <button class="btn-item revoke-btn layout-y:gap-1 cursor-pointer" @click="callBackBets">
        <img src="/images/revoke-icon.png" alt="revoke" class="block size-5 object-contain" />
        <span class="text-white/80 layout-x:text-xs layout-y:text-default">{{ $t('revoke') }}</span>
      </button>
      <button class="btn-item trend-history-btn layout-y:hidden! before:left-0 cursor-pointer"
              @click="toggleHistoryShow">
        <img src="/images/trend-history-icon.png" alt="trend history" class="block size-5 object-contain" />
        <span class="text-white/80 layout-x:text-xs layout-y:text-default">{{ $t('trend_history') }}</span>
      </button>
    </div>
  </section>
</template>

<style lang="scss" scoped>
:where([data-layout="x"]) {
  .bet-bar-widget {
    .btn-box {
      width: 182px;

      &.left-box {
        clip-path: polygon(0 0, 134px 0, 100% 100%, 0% 100%);
      }

      &.right-box {
        clip-path: polygon(48px 0, 100% 0%, 100% 100%, 0% 100%);
      }

      .btn-item {
        min-width: 60px;

        svg {
          width: 20px;
          height: 20px;
        }
      }
    }

    .action-box {
      --chip-size: 30px;
    }
  }
}

:where([data-layout="y"]) {
  .bet-bar-widget {
    .btn-box {
      width: 97px;

      &.left-box {
        clip-path: polygon(0 0, 51.84px 0, 100% 100%, 0% 100%);
      }

      &.right-box {
        clip-path: polygon(54px 0, 100% 0%, 100% 100%, 0% 100%);
      }

      .btn-item svg {
        width: 14px;
        height: 14px;
      }
    }

    .action-box {
      --chip-size: 32px;
    }
  }
}

.bet-bar-widget {
  height: 54px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  background: linear-gradient(90deg, #860727 0%, rgba(134, 7, 39, 0) 40%, transparent 41%, transparent 59%, rgba(134, 7, 39, 0) 60%, #860727 100%) top/100% 2px no-repeat,
  linear-gradient(90deg, #860727 0%, rgba(134, 7, 39, 0) 45%, transparent 46%, transparent 54%, rgba(134, 7, 39, 0) 55%, #860727 100%) bottom/100% 2px no-repeat;
}

.btn-box {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  height: 100%;
  background: linear-gradient(180deg, #BA002D 0%, #75001D 100%);
  box-shadow: -1px -1px 1px 0px rgba(0, 0, 0, 0.25) inset,
  1px 1px 1px 0px rgba(0, 0, 0, 0.25) inset;

  .btn-item {
    user-select: none;
    padding-inline: 10px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &.layout-y\:hidden\! {
      position: relative;

      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        content: '';
        width: 1px;
        height: 34px;
        background-color: rgba(255, 255, 255, .3);
      }
    }
  }
}

.action-box {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  margin-inline: -14px;
  user-select: none;
}

.switch-status-enter-active,
.switch-status-leave-active {
  transition: all 0.3s ease-out;
}

.switch-status-enter-from {
  transform: rotateX(0);
  opacity: 0;
}

.switch-status-leave-to {
  transform: rotateX(180deg);
  opacity: 0;
}
</style>