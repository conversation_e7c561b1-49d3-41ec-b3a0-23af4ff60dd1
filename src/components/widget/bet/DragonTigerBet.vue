<script setup lang="ts">
import {onMounted, onUnmounted, ref, watch} from "vue";
import {useI18n} from "vue-i18n";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore, useUserStore} from "@/stores";
import {GameStatus, SpecialBet} from "@/constant/gameData.ts"
import {openMessage} from "@/components/ui/GMessage/index.ts";
import emitter from "@/utils/emitter.ts";
import {getBoundingClientRect} from "@/utils/utils.ts";

import EffectWin from "@/components/widget/EffectWin.vue"

//缩放比例
const {scale} = storeToRefs(useMainStore());
const {
  gameId,
  gameRoundId,
  gamePlayType,
  gameStatus,
  gameWinBet,
  betChipsResult,
  playerBetDTResult
} = storeToRefs(useGameStore())
const {setBetChipsResult, setPlayerBetDTResult} = useGameStore();
const {t} = useI18n();
const {balance} = storeToRefs(useUserStore());

//玩家下注结果
const betResult = ref({});

emitter.on('game-bet-amount', (data: SocketData) => {
  if (data?.data?.code === 200) {
    betResult.value = {...data.data.data};
    setBetChipsResult({...data.data.data});
  }
});

const isDragonTiger = ref("");
const isBigger = ref("");
const isSingular = ref("");
const lists = ref([
  {
    type: 2,
    location: 1,
    amount: '2-1',
    title: SpecialBet.DRAGON,
    odds: "1:0.95",
    image: "/images/bet-dragon.png",
    bets: 0,
    count: 0,
  },
  {
    type: 3,
    location: 1,
    amount: '3-1',
    belong: "dragon",
    title: SpecialBet.BIG,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: 4,
    location: 1,
    amount: '4-1',
    belong: "dragon",
    title: SpecialBet.SINGULAR,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: 4,
    location: 2,
    amount: '4-2',
    belong: "tiger",
    title: SpecialBet.EVEN,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: 3,
    location: 2,
    amount: '3-2',
    belong: "tiger",
    title: SpecialBet.SMALL,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: 2,
    location: 2,
    amount: '2-2',
    title: SpecialBet.TIGER,
    odds: "1:0.95",
    image: "/images/bet-tiger.png",
    bets: 0,
    count: 0,
  },
]);

// 定义一个函数来处理下注并返回 Promise
const waitForBetResponse = () => {
  return new Promise<{ success: boolean; data?: any }>((resolve) => {
    const handler = (data: any) => {
      // 移除事件监听器，避免内存泄漏
      emitter.off("user-bet-res", handler);
      if (data?.data?.code === 200) {
        resolve({success: true, data});
      } else {
        resolve({success: false});
      }
    };
    emitter.on("user-bet-res", handler);
  });
};

// 投放筹码
const takeBet = async (event: MouseEvent, index: any) => {
  //检查游戏阶段
  if (gameStatus.value !== GameStatus.GAME_BET) {
    return openMessage({message: t('no_bets_available_tips')});
  }

  const target = event.currentTarget as HTMLElement;
  const chip = document.querySelector(".chip-item.active") as HTMLElement;

  if (!chip) return;

  //检查下注是否冲突
  if (isBetConflict(index)) {
    return;
  }
  if (isBiggerConflict(index)) {
    return;
  }
  if (isSingularConflict(index)) {
    return;
  }

  //获取下注金额
  const count = document.querySelector(".chip-item.active")?.getAttribute("data-value")
  if (balance.value < Number(count)) {
    return;
  }
  //   下注
  emitter.emit("user-bet-req", {
    betAmount: count,
    gameId: gameId.value,
    roundId: gameRoundId.value,
    type: lists.value[index].type,
    location: lists.value[index].location,
  });

  // 等待下注响应
  const response = await waitForBetResponse();
  if (!response.success) {
    return;
  }

  emitter.emit('play-sound', 'SOUND_5');

  /**下注动画 */
      //复制筹码元素
  const chipClone = chip.cloneNode(true) as HTMLElement;
  chipClone.innerText = "";
  chipClone.style.position = "absolute";
  chipClone.style.zIndex = "9999";
  chipClone.style.pointerEvents = "none";
  chipClone.style.backgroundImage = "url(/images/bets-1.png)"
  chipClone.style.backgroundSize = "contain";
  chipClone.style.backgroundPosition = "center";
  chipClone.style.backgroundRepeat = "no-repeat";
  document.querySelector('.view-content').appendChild(chipClone);

  // 获取目标元素的位置
  const targetRect = getBoundingClientRect(target);

  // 计算目标元素中心位置相对于页面的坐标
  const targetCenterX = (targetRect.left + targetRect.width - 21);
  const targetCenterY = (targetRect.top + targetRect.height - 14);

  // 获取筹码元素的位置
  const chipRect = getBoundingClientRect(chip);

  const viewContent = getBoundingClientRect(document.querySelector(".view-content"));

  chipClone.style.width = `${chipRect.width}px`;
  chipClone.style.height = `${chipRect.height}px`;
  chipClone.style.left = `${viewContent.width / 2 - chipRect.width / 2}px`;
  chipClone.style.bottom = `${30 / scale.value}px`;

  const chipCloneRect = getBoundingClientRect(chipClone);

  // 计算筹码元素中心位置相对于页面的坐标
  const chipCenterX =
      (chipCloneRect.left + chipCloneRect.width / 2);
  const chipCenterY =
      (chipCloneRect.top + chipCloneRect.height / 2);

  // 计算筹码需要移动的距离
  const deltaX = targetCenterX - chipCenterX;
  const deltaY = targetCenterY - chipCenterY;

  // // 设置筹码的过渡效果
  chipClone.style.transition = "transform 0.5s ease-out, width 1.5s ease-out, height 1.5s ease-out";
  // 移动筹码
  chipClone.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
  // 逐渐缩小宽度和高度
  chipClone.style.width = `18px`; // 可根据需求调整最终宽度
  chipClone.style.height = `18px`; // 可根据需求调整最终高度

  // 动画结束后重置筹码位置
  setTimeout(() => {
    chipClone.remove();
    lists.value[index].bets += 1;
    lists.value[index].count += Number(count);
    setPlayerBetDTResult({recordLists: lists.value, roundId: gameRoundId.value});
    emitter.emit("get-user-balance-req");
  }, 500);
};

// 定义一个函数来处理撤销下注并返回 Promise
const waitForBackBetResponse = () => {
  return new Promise<{ success: boolean; data?: any }>((resolve) => {
    const handler = (data: any) => {
      // 移除事件监听器，避免内存泄漏
      emitter.off("user-revoke-res", handler);
      if (data?.data?.data.flag) {
        resolve({success: true});
      } else {
        resolve({success: false});
      }
    };
    emitter.on("user-revoke-res", handler);
  });
};

const backBets = async () => {
  // 撤销下注
  emitter.emit('user-revoke-req', {
    roundId: gameRoundId.value,
  })
  // 等待撤销下注响应
  const response = await waitForBackBetResponse();
  if (!response.success) return
  //重新获取余额
  emitter.emit('get-user-balance-req');
  //更新下注总额为0
  emitter.emit('update-bet-total-amount', {roundId: gameRoundId.value, count: 0});
  //撤销筹码动画
  const chip = document.querySelector(".chip-item.active") as HTMLElement;
  const chipRect = getBoundingClientRect(chip);
  const chipCenterX = (chipRect.left + chipRect.width / 2);
  const chipCenterY = (chipRect.top + chipRect.height / 2);
  document.querySelectorAll(".bets-img").forEach((item: any) => {
    const res = getBoundingClientRect(item);
    const x = (res.left + res.width / 2);
    const y = (res.top + res.height / 2);
    const deltaX = chipCenterX - x;
    const deltaY = chipCenterY - y;
    item.style.transition = "transform 0.5s ease-out";
    item.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    item.style.position = "relative";
    item.style.zIndex = "9999";
  });
  // 动画结束后重置筹码数据
  setTimeout(() => {
    lists.value.forEach((item: any) => {
      item.bets = 0;
      item.count = 0;
    })
    isDragonTiger.value = "";
    isBigger.value = "";
    isSingular.value = ""
    setPlayerBetDTResult({})
  }, 500);
};

//判断龙虎下注是否冲突
const isBetConflict = (index: Number) => {
  const string_arr = [SpecialBet.DRAGON, SpecialBet.TIGER];
  if (
      isDragonTiger.value &&
      string_arr.includes(lists.value[index].title) &&
      isDragonTiger.value != lists.value[index].title
  ) {
    openMessage({message: t('no_bets_available_type_tips')});
    return true;
  }
  if (string_arr.includes(lists.value[index].title)) {
    isDragonTiger.value = lists.value[index].title;
    return false;
  }
};

//判断大小下注是否冲突
const isBiggerConflict = (index: Number) => {
  const string_arr = [SpecialBet.BIG, SpecialBet.SMALL];
  if (
      isBigger.value &&
      string_arr.includes(lists.value[index].title)
  ) {
    if (isBigger.value != lists.value[index].belong) {
      openMessage({message: t('no_bets_available_size_tips')});
      return true;
    }
  }
  if (string_arr.includes(lists.value[index].title)) {
    isBigger.value = lists.value[index].belong;
    return false;
  }
};

//判断单双下注是否冲突
const isSingularConflict = (index: Number) => {
  const string_arr = [SpecialBet.SINGULAR, SpecialBet.EVEN];
  if (
      isSingular.value &&
      string_arr.includes(lists.value[index].title)
  ) {
    if (isSingular.value != lists.value[index].belong) {
      openMessage({message: t('no_bets_available_odd_even_tips')});
      return true;
    }
  }
  if (string_arr.includes(lists.value[index].title)) {
    isSingular.value = lists.value[index].belong
    return false;
  }
};

const getShowWin = (item: any) => {
  if (item.title === SpecialBet.DRAGON) return gameWinBet.value.type2Loc === 1;
  if (item.title === SpecialBet.TIGER) return gameWinBet.value.type2Loc === 2;
  if (item.title === SpecialBet.BIG) return gameWinBet.value.type3Loc === 1;
  if (item.title === SpecialBet.SMALL) return gameWinBet.value.type3Loc === 2;
  if (item.title === SpecialBet.SINGULAR) return gameWinBet.value.type4Loc === 1;
  if (item.title === SpecialBet.EVEN) return gameWinBet.value.type4Loc === 2;
}

watch(
    gamePlayType,
    (newValue, oldValue) => {
      if (newValue) {
        lists.value.forEach((item: any) => {
          let index = newValue.findIndex((i: any) => i.playTypeId == item.type);
          item.odds = `1:${newValue[index].rate[0]}`;
        });
      }
    },
    {deep: true}
)

watch(
    gameStatus,
    (newValue, oldValue) => {
      if (newValue == 'game-wait') {
        lists.value.forEach((item: any) => {
          item.bets = 0;
          item.count = 0;
        });
      }
    }
)

// 转换数值为 1k, 2.5k, 1m 等格式的函数
const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    const value = num / 1000000000;
    return Number.isInteger(value) ? `${value}m` : `${value.toFixed(1).replace(/\.0$/, '')}B`;
  }
  if (num >= 100000) {
    const value = num / 100000;
    return Number.isInteger(value) ? `${value}m` : `${value.toFixed(1).replace(/\.0$/, '')}M`;
  }
  if (num >= 1000) {
    const value = num / 1000;
    return Number.isInteger(value) ? `${value}k` : `${value.toFixed(1).replace(/\.0$/, '')}K`;
  }
  return num.toString();
};

onMounted(() => {
  emitter.on("back-tiger-bets", backBets);
  if (Object.keys(betChipsResult.value).length !== 0) {
    betResult.value = {...betChipsResult.value};
  } else {
    betResult.value = JSON.parse(localStorage.getItem("playerBetResult") || "{}").betChipsResult;
  }
  if (Object.keys(playerBetDTResult.value).length !== 0 && gameRoundId.value == playerBetDTResult.value.roundId) {
    lists.value = [...playerBetDTResult.value.recordLists];
  } else {
    try {
      const playerBetResult = JSON.parse(localStorage.getItem("playerBetResult") || "{}");
      const {playerBetDTResult: playerbet, gameRoundId: roundId} = playerBetResult;
      if (roundId === playerbet?.roundId) {
        lists.value = [...playerbet.recordLists];
      }
    } catch (e) {
    }
  }
});

onUnmounted(() => {
  emitter.off("back-tiger-bets");
  emitter.off("game-bet-amount");
});
</script>

<template>
  <div class="dragon-tiger-bet-widget grid layout-x:grid-cols-2 layout-y:grid-cols-1 layout-x:gap-0.5 layout-y:gap-1.5">
    <div class="flex layout-x:gap-0.5 layout-y:gap-2">
      <section
          v-for="(item, index) in lists.slice(0, 3)"
          :key="index"
          class="dragon-tiger-bet-item cursor-pointer dragon flex-1"
          @click="takeBet($event, index)"
      >
        <EffectWin v-if="getShowWin(item)"/>
        <div class="bet-item--top">
          <img v-show="item.image" :src="item.image" alt="dragon"/>
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="odds">{{ item.odds }}</div>
          </div>
        </div>
        <div class="bet-item--bottom flex align-center justify-between">
          <span class="bets-text" v-if="gameStatus !== 'game-wait'">
            {{ formatNumber(item.count) }}/{{ formatNumber(betResult[item.amount] ?? 0) }}
          </span>
          <span v-else class="bets-text">bets</span>
          <template v-if="item.bets != 0">
            <img :src="`/images/bets-${Math.min(item.bets, 3)}.png`" :alt="`${index}-bet`" class="bets-img h-[14px]"/>
          </template>
        </div>
      </section>
    </div>
    <div class="flex layout-x:gap-0.5 layout-y:gap-2 layout-y:flex-row-reverse">
      <section
          v-for="(item, index) in lists.slice(3)"
          :key="index"
          class="dragon-tiger-bet-item cursor-pointer tiger flex-1"
          @click="takeBet($event, index + 3)"
      >
        <EffectWin v-if="getShowWin(item)"/>
        <div class="bet-item--top">
          <img v-show="item.image" :src="item.image" alt="dragon"/>
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="odds">{{ item.odds }}</div>
          </div>
        </div>
        <div class="bet-item--bottom flex align-center justify-between">
          <span class="bets-text" v-if="gameStatus !== 'game-wait'">
            {{ formatNumber(item.count) }}/{{ formatNumber(betResult[item.amount] ?? 0) }}
          </span>
          <span v-else class="bets-text">bets</span>
          <template v-if="item.bets != 0">
            <img :src="`/images/bets-${Math.min(item.bets, 3)}.png`" :alt="`${index}-bet`" class="bets-img h-[14px]"/>
          </template>
        </div>
      </section>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dragon-tiger-bet-item.dragon {
  box-shadow: inset 0 0 1px 1px #001e80;
  background: url("@/assets/svg/MobileDragonBetGridBg.svg") right top no-repeat,
  linear-gradient(to right, #112c84 0%, #0026a7 7.5%, #00124e 100%) center/100% no-repeat;
}

.dragon-tiger-bet-item.dragon .bet-item--top .title {
  background-image: linear-gradient(180deg, #e4f9ff 25%, #b9ccff 100%);
}

.dragon-tiger-bet-item.tiger {
  box-shadow: inset 0 0 1px 1px #72001c;
  background: url("@/assets/svg/MobileTigerBetGridBg.svg") right top no-repeat,
  linear-gradient(to right, #a9001a, #4d000c) center/100% no-repeat;
}

.dragon-tiger-bet-item.tiger .bet-item--top .title {
  background-image: linear-gradient(180deg, #fff3e6 25%, #ffad76 100%);
}

.dragon-tiger-bet-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 6px;
  width: 100%;
  height: 66px;
}

.dragon-tiger-bet-item::after {
  opacity: 0;
  pointer-events: none;
  position: absolute;
  inset: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  transition: opacity 0.6s ease;
  transform: scale(1.03);
  content: "";
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url("/images/bet-light-border1.png");
}

.dragon-tiger-bet-item:hover::after {
  transition: none;
  opacity: 1;
}

.dragon-tiger-bet-item .bet-item--top {
  display: flex;
  gap: 4px;
  width: 100%;
}

.dragon-tiger-bet-item .bet-item--top img {
  flex-shrink: 0;
  display: block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.dragon-tiger-bet-item .bet-item--top .title {
  line-height: 1.2;
  font-size: 12px;
  font-weight: bolder;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dragon-tiger-bet-item .bet-item--top .odds {
  margin-top: 4px;
  line-height: 1;
  color: white;
  font-size: 14px;
  font-weight: 700;
}

.dragon-tiger-bet-item .bet-item--bottom .bets-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: normal;
}

.chip {
  position: fixed;
  width: 20px;
  height: 20px;
  background-color: gold;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  z-index: 9999;
  // 初始位置，可根据需求调整
  top: 543px;
  left: 10px;
}
</style>
