<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore, useMainStore } from "@/stores";

import Chips from "./Chips.vue";
import emitter from "@/utils/emitter.ts";

const { isVertical } = storeToRefs(useMainStore());
const { betChipsList, activeChipIndex } = storeToRefs(useGameStore());
const { setActiveChipIndex} = useGameStore();

const swiperRef = ref();
// 筹码切换速度
const slideSpeed = ref(500);
const showCount = computed(() => isVertical.value ? 3 : 5);
// 筹码偏移量（手动设为居中）
const chipOffset = computed(() => Math.floor(showCount.value / 2))

const showChipList = computed(() => {
  let result = betChipsList.value;
  // 筹码数量需要大于展示的数量
  while (result.length <= showCount.value && betChipsList.value.length) {
    result = result.concat(betChipsList.value);
  }
  return result;
});

const slideChangeHandler = (swiper: any) => {
  setActiveChipIndex((swiper.realIndex + chipOffset.value) % showChipList.value.length)
  emitter.emit('play-sound', 'SOUND_1');
};

/**
 * 切换到指定筹码
 * @param index 目标筹码索引
 * @param speed 切换速度
 */
const slideToIndex = (index: number, speed?: number) => {
  if (!(swiperRef.value && swiperRef.value.swiper)) return
  const toIndex = (index - chipOffset.value + showChipList.value.length) % showChipList.value.length
  swiperRef.value.swiper.slideToLoop(toIndex, speed)
}

onMounted(() => {
  if (!swiperRef.value) return
  nextTick(() => slideToIndex(activeChipIndex.value, 0))
  swiperRef.value.swiper.on('slideChange', slideChangeHandler);
});

onUnmounted(() => {
  swiperRef.value && swiperRef.value.swiper.destroy();
});
</script>

<template>
  <div class="w-full h-full flex justify-center items-center">
    <div class="switch-btn switch-chip-prev">
      <img src="/images/arrow-left.png" alt="arrow left" class="block size-full" />
    </div>
    <div class="flex-1 w-1 h-full">
      <swiper-container
          ref="swiperRef"
          :navigation="{
            prevEl: '.switch-chip-prev',
            nextEl: '.switch-chip-next',
          }"
          :slidesPerView="showCount"
          :speed="slideSpeed"
          :allowTouchMove="false"
          :loop="true"
          :loopAdditionalSlides="1"
          class="h-full"
      >
        <swiper-slide
            v-for="(item, index) in showChipList"
            :key="item"
            class="flex items-center"
        >
          <Chips :idx="index" :amount="item" :checked="activeChipIndex === index" @click="slideToIndex(index)"/>
        </swiper-slide>
      </swiper-container>
    </div>
    <div class="switch-btn switch-chip-next">
      <img src="/images/arrow-right.png" alt="arrow right" class="block size-full" />
    </div>
  </div>
</template>

<style scoped>
.switch-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--chip-size);
  height: var(--chip-size);
  cursor: pointer;
}
</style>