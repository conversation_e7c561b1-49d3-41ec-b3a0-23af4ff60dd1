<script setup lang="ts">
import {onMounted, onUnmounted, ref, watch} from "vue";
import {storeToRefs} from "pinia";
import {animate} from "animejs";
import {useGameStore, useMainStore} from "@/stores";
import {GameStatus} from "@/constant/gameData.ts";
import {formatNumber} from "@/utils/utils.ts";
import emitter from "@/utils/emitter";

const {hBetPanelShow} = storeToRefs(useMainStore());
const {toggleBetPanelShow} = useMainStore();
const {betChipsResult, gameStatus, playerBetDTResult, playerBetChipsResult} = storeToRefs(useGameStore());
const {setBetChipsResult} = useGameStore();

const polygonRef = ref();

const arrowExpandPath = '10.1449,0 0,8 7.05882,4.70588 10.1449,3.40164 12.9412,4.70588 20,8 10.1449,0';
const arrowClosePath = '10.1449,8 0,0 7.05882,3.29412 10.1449,4.59836 12.9412,3.29412 20,0 10.1449,8';

//玩家下注结果
const betResult = ref<any>({});
const totalBet = ref(0);
emitter.on('game-bet-amount', (data: SocketData) => {
  if (data?.data?.code === 200) {
    betResult.value = {...data.data.data};
    setBetChipsResult({...data.data.data});
  }
});

watch(hBetPanelShow, (show) => {
  animate(polygonRef.value, {
    points: show ? arrowClosePath : arrowExpandPath,
    duration: 300,
    alternate: true,
    onComplete: self => self.cancel()
  });
});

watch(gameStatus, (status) => {
  if (status === GameStatus.GAME_WAIT) {
    totalBet.value = 0;
  }
})

watch([playerBetChipsResult, playerBetDTResult], () => {
  totalBet.value = 0;
  if (playerBetChipsResult.value.recordLists?.length !== 0) {
    playerBetChipsResult.value.recordLists?.forEach((item: any) => {
      totalBet.value += item.count;
    })
  }
  if (playerBetDTResult.value.recordLists?.length !== 0) {
    playerBetDTResult.value.recordLists?.forEach((item: any) => {
      totalBet.value += item.count;
    })
  }
}, {deep: true});

const getBetInfo = () => {
  if (Object.keys(betChipsResult.value).length !== 0) {
    betResult.value = {...betChipsResult.value};
  } else {
    betResult.value = JSON.parse(localStorage.getItem("playerBetResult") || "{}").betChipsResult;
  }
  const playerBetResult = JSON.parse(localStorage.getItem("playerBetResult") || "{}");
  const {playerBetDTResult: dtBet, playerBetChipsResult: chipBet, gameRoundId: roundId} = playerBetResult;
  if (roundId === (dtBet?.roundId ?? '0') && dtBet.recordLists?.length !== 0) {
    dtBet.recordLists?.forEach((item: any) => {
      totalBet.value += item.count;
    })
  }
  if (roundId === (chipBet?.roundId ?? '0') && chipBet.recordLists?.length !== 0) {
    chipBet.recordLists?.forEach((item: any) => {
      totalBet.value += item.count;
    })
  }
}
onMounted(() => {
  getBetInfo();
})

onUnmounted(() => {
  emitter.off('game-bet-amount');
})

</script>

<template>
  <div class="user-amount-inner">
    <img class="size-4 block mr-0.5" src="/images/total-bet-amount.png" alt="total bet amount">

    <div class="text-[#efe70c] font-bold">＄{{ formatNumber(totalBet) }}</div>
    <div class="text-white/80 mx-1">/</div>
    <div class="text-white/80">＄{{ formatNumber(betResult.total ?? 0) }}</div>

    <div class="layout-y:hidden ml-4 cursor-pointer" @click="toggleBetPanelShow()">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="8" viewBox="0 0 20 8" fill="none">
        <polygon ref="polygonRef" fill="white"
                 points="10.1449,0 0,8 7.05882,4.70588 10.1449,3.40164 12.9412,4.70588 20,8 10.1449,0"/>
      </svg>
    </div>
  </div>
</template>

<style scoped>
:where([data-layout="x"]) .user-amount-inner {
  min-width: 178px;
  background-image: url("/images/bet-amount-bg-x.png");
}

:where([data-layout="y"]) .user-amount-inner {
  min-width: 141px;
  background-image: url("/images/bet-amount-bg-y.png");
}

.user-amount-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  line-height: 20px;
  padding-inline: 16px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center bottom;
  transform: translate3d(0, 0, 0);
}
</style>