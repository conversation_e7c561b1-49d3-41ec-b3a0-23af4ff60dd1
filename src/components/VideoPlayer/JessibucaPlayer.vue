<script setup lang="ts">
import {computed, onMounted, onUnmounted, watch} from "vue";
import {storeToRefs} from "pinia";
import {debounce, sleep} from "radash";
import {useGameStore, useMainStore} from "@/stores";
import {CameraType} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter.ts";

const props = defineProps<{
  type: CameraType;
}>()

const { cameraInfo, isShowVideoLoading } = storeToRefs(useMainStore());
const { videoStreamList } = storeToRefs(useGameStore());
const { toggleVideoLoading } = useMainStore();

let jessibuca: Jessibuca | null = null;

const isCurrPlayer = computed(() => props.type === cameraInfo.value.type);

const playUrl = computed(() => {
  const { quality } = cameraInfo.value;
  const { a, b } = videoStreamList.value.domestic?.[quality] || {};
  const line = props.type === CameraType.A ? a : b;
  return line?.url;
})

const setupJessibucaPlayer = async () => {
  await stopPlayer(true);

  jessibuca = new Jessibuca({
    container: `#jessibuca-player-${props.type}`,
    // debug: true,
    videoBuffer: 0.2,
    isResize: false,
    // isFullResize: true,
    keepScreenOn: true,
    // hiddenAutoPause: true,
    controlAutoHide: true,
  });

  jessibuca.toggleControlBar(false)

  jessibuca.on("start", function () {
    toggleVideoLoading(false)
  })

  jessibuca.on("error", function (error: any) {
    console.log('error:', error)
    if (error === jessibuca.ERROR.fetchError) {
      //
    } else if (error === jessibuca.ERROR.webcodecsH265NotSupport) {
      //
    }
  })
}

const stopPlayer = async (isDestroy?: boolean) => {
  if (!isCurrPlayer.value) return;
  if (!jessibuca) return;

  await jessibuca.pause();

  if (isDestroy) {
    jessibuca.destroy();
    jessibuca = null;
  }
}

const startPlayer = async () => {
  if (!isCurrPlayer.value) return;
  if (!jessibuca) return;
  if (!playUrl.value) return;

  toggleVideoLoading(true);
  await jessibuca.play(playUrl.value)
}

const handleVisible = () => {
  if (document.visibilityState === 'visible') {
    startPlayer();
  } else {
    stopPlayer();
  }
}

const handleUpdate = async () => {
  // if (isShowVideoLoading.value) return;
  if (!isCurrPlayer.value) return;
  toggleVideoLoading(true);
  await setupJessibucaPlayer();
  await sleep(600);
  await startPlayer();
}

const debounceUpdate = debounce({ delay: 600 }, handleUpdate);

onMounted(() => {
  // emitter.on('show-switch-scenes', startPlayer);
  window.addEventListener('visibilitychange', handleVisible);

  watch([cameraInfo, videoStreamList], debounceUpdate, {
    deep: true,
    immediate: true,
  });
});

onUnmounted(() => {
  // emitter.off('show-switch-scenes', startPlayer);
  window.removeEventListener('visibilitychange', handleVisible);

  stopPlayer(true);
});
</script>

<template>
<div class="size-full jussibuca-player" :id="`jessibuca-player-${type}`"></div>
</template>

<style lang="scss" scoped>
.jussibuca-player {
  :deep(canvas) {
    width: 100% !important;
    height: 100% !important;
    inset: 0 !important;
    transform: scale(1) !important;
  }
}
</style>