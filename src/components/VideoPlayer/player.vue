<script setup lang="ts">
import {computed, onMounted, onUnmounted, watch} from "vue";
import {storeToRefs} from "pinia";
import {sleep} from "radash";
import {useGameStore, useMainStore} from "@/stores";
import {CameraType} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter.ts";

const props = defineProps<{
  type: CameraType;
}>()

const { cameraInfo, isShowVideoLoading } = storeToRefs(useMainStore());
const { videoStreamList } = storeToRefs(useGameStore());
const { toggleVideoLoading } = useMainStore();

let nodePlayer: NodePlayer | any = undefined;

const isCurrPlayer = computed(() => props.type === cameraInfo.value.type);

const stopPlayer = () => {
  if (!isCurrPlayer.value) return;
  nodePlayer && nodePlayer.stop();
}

const startPlayer = async () => {
  if (!isCurrPlayer.value) return;
  toggleVideoLoading(true);
  const { quality } = cameraInfo.value;
  const { a, b } = videoStreamList.value.domestic?.[quality] || {};
  const line = props.type === CameraType.A ? a : b;
  if (line && line.url && nodePlayer) {
    nodePlayer.stop();
    await sleep(600);
    nodePlayer.start(line.url);
  }
}

const setupNodePlayer = () => {
  if (nodePlayer) nodePlayer.stop();
  // v0.9.19版之后,在Android手机端推荐使用以下音频引擎
  if (/(Android)/i.test(navigator.userAgent)) {
    NodePlayer.activeAudioEngine(true);
  }
  nodePlayer = new NodePlayer();
  nodePlayer.setBufferTime(2e3);
  nodePlayer.setKeepScreenOn();
  nodePlayer.setTimeout(10);
  nodePlayer.setView(`video-box-${props.type}`, false);
  nodePlayer.on("start", () => {
    toggleVideoLoading(false);
  });
  nodePlayer.on("error", (err: any) => {
    console.log("=======err:", err);
  });
}

const handleUpdate = () => {
  if (isShowVideoLoading.value) return;
  startPlayer();
}

const handleVisible = () => {
  if (document.visibilityState === 'visible') {
    startPlayer();
  } else {
    stopPlayer();
  }
}

onMounted(() => {
  emitter.on('show-switch-scenes', startPlayer);
  window.addEventListener('visibilitychange', handleVisible);

  setupNodePlayer();

  watch([cameraInfo, videoStreamList], handleUpdate, {
    deep: true,
    immediate: true,
  });
});

onUnmounted(() => {
  emitter.off('show-switch-scenes', startPlayer);
  window.removeEventListener('visibilitychange', handleVisible);

  stopPlayer();
});
</script>

<template>
  <canvas class="video-box" :id="`video-box-${type}`"></canvas>
</template>

<style scoped>
.video-box {
  display: block;
  width: 100%;
  height: 100%;
  background-color: black;
}
</style>