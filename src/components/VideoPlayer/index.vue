<script setup lang="ts">
import {onMounted, ref, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";
import {CameraType} from "@/constant/gameData.ts";

import Loading from "@/components/loading/index.vue";
import Player from "./player.vue";
import JessibucaPlayer from "./JessibucaPlayer.vue"

const { cameraInfo, isShowVideoLoading } = storeToRefs(useMainStore());
const { toggleCameraType } = useMainStore();

const swiperRef = ref<any>(null);

const isInitNodePlayer = ref<boolean>(true);

onMounted(() => {
  watchEffect(() => {
    if (cameraInfo.value.type === CameraType.A) {
      swiperRef.value?.swiper?.slideTo(0);
    }
    if (cameraInfo.value.type === CameraType.B) {
      swiperRef.value?.swiper?.slideTo(1);
    }
  });

  swiperRef.value?.addEventListener('swiperslidechange', (event: CustomEvent) => {
    const active = event.detail?.[0]?.activeIndex;
    if (active === 0 && cameraInfo.value.type !== CameraType.A) {
      toggleCameraType(CameraType.A);
    }
    if (active === 1 && cameraInfo.value.type !== CameraType.B) {
      toggleCameraType(CameraType.B);
    }
  });

  // NodePlayer.load(() => {
  //   console.log("node player load....")
  //   NodePlayer.debug(true);
  //   isInitNodePlayer.value = true;
  // });
})
</script>

<template>
  <Loading class="size-full" :show="isShowVideoLoading">
    <div class="video-player-wrapper size-full">
      <div class="video-player-box size-full">
        <swiper-container ref="swiperRef" class="h-full">
          <swiper-slide>
            <JessibucaPlayer v-if="isInitNodePlayer" :type="CameraType.A" />
          </swiper-slide>
          <swiper-slide>
            <JessibucaPlayer v-if="isInitNodePlayer" :type="CameraType.B" />
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </Loading>
</template>

<style scoped>

</style>