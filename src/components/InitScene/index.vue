<script setup lang="ts">
import {watch} from "vue";
import {useAssetLoader} from "@/utils/AssetLoader.ts";

const emits = defineEmits<{
  (e: 'finish'): void
}>()

const { finish, progress, start } = useAssetLoader();

fetch('/manifest.json')
  .then(res => res.json())
  .then(manifest => start(manifest));

watch(finish, (done) => done && emits('finish'));
</script>

<template>
<div class="loading-wrapper fixed inset-0 size-full bg-(--bg-color) flex gap-8 items-center justify-center flex-col text-xl">
  <img class="block layout-y:w-27 layout-x:w-24.5" src="/images/logo-2.png" alt="g-bet" />
  <div class="loading-bar relative h-2 layout-x:w-82.5 layout-y:w-67.5">
    <div ref="lineRef" class="progress-line bg-white rounded-full"></div>
    <div ref="dotRef" class="progress-dot"></div>
  </div>
</div>
</template>

<style lang="scss" scoped>
:where([data-layout="y"]) .loading-wrapper {
  background-image: url('/images/loading-bg-y.png');
}
:where([data-layout="x"]) .loading-wrapper {
  background-image: url('/images/loading-bg-x.png');
}

.loading-wrapper {
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.loading-bar {
  border-radius: 4px;
  box-shadow: 
    0 0.1px 0.5px 0 #BCBCBC inset,
    0 -0.1px 0.5px 0 #BCBCBC inset;
}

.progress-line {
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  background: linear-gradient(90deg, #FF34B5 0%, #F9003D 100%);
  box-shadow: 0 0 7px 0 #FA064C;
  height: 100%;
  width: calc(v-bind(progress) * 100%);

  &::after {
    content: '';
    position: absolute;
    background: linear-gradient(90deg, #FF34B5 0%, #F9003D 100%);
    width: calc(1 / v-bind(progress) * 100%);
    height: 100%;
  }
}

.progress-dot {
  position: absolute;
  content: '';
  top: 0;
  bottom: 0;
  margin: auto;
  transform: translateX(-50%);
  left: calc(v-bind(progress) * 100%);
  height: 13px;
  width: 13px;
  filter: blur(5px);
  background-color: #FA0447;
  animation: dot-zoom 2s ease infinite;
  opacity: 0.6;
}

@keyframes dot-zoom {
  0%, 100% {
    transform: translateX(-50%) scale(1);
  }
  50% {
    transform: translateX(-50%) scale(1.4);
  }
}
</style>