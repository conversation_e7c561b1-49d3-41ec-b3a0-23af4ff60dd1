<script setup lang="ts">
  defineProps<{
    show: boolean;
  }>()
</script>

<template>
  <div class="loading-wrapper relative">
    <slot />
    <div v-if="show">
      <div class="loading-box absolute z-1 inset-0 size-full ">
        <div class="absolute inset-0 bg-black/50"></div>
        <div class="absolute inset-0 flex flex-col items-center justify-center gap-1.5">
          <img class="block size-15" src="/images/loading.png" alt="loading">
          <div class="light-bar mb-1.5"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading-wrapper {
  .light-bar {
    width: 144px;
    height: 2px;
    border-radius: 1px;
    overflow: hidden;
    background-image: linear-gradient(
      to right,
      transparent 0%,
      white 40%,
      white 60%,
      transparent 100%
    );
    background-position: 50% 0;
    background-repeat: no-repeat;
    animation: run 1.2s linear infinite;
  }
}

@keyframes run {
  0% {
    background-position: -144px;
  }

  100% {
    background-position: 144px;
  }
}
</style>