<template>
  <GModal v-model:show="showModal" :title="$t('rules')">
    <Loading :show="showLoading">
      <div class="rules-content">
        <div v-html="gameRules"></div>
      </div>
    </Loading>
  </GModal>
</template>

<script setup lang="ts">
import {onMounted, onUnmounted, ref} from "vue";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore} from "@/stores";
import emitter from "@/utils/emitter.ts";

import GModal from "@/components/ui/GModal/index.vue";
import Loading from "@/components/loading/index.vue";

const { lang } = storeToRefs(useMainStore());
const { gameRules } = storeToRefs(useGameStore());

const showModal = ref<boolean>(false);

const showLoading = ref<boolean>(false);

const show = () => {
  showModal.value = true;
}

const hide = () => {
  showModal.value = false;
}

const hideLoading = () => {
  showLoading.value = false;
}

onMounted(() => {
  show();
  showLoading.value = true;
  emitter.on('get-game-rules-res', hideLoading);
  emitter.emit('get-game-rules-req', { language: lang.value });
});

onUnmounted(() => {
  emitter.off('get-game-rules-res', hideLoading);
})

defineExpose({ show, hide })
</script>

<style lang="scss" scoped>
:deep(.rules-content) {
  color: #fff;
  font-size: 12px;
  line-height: 18px;

  h1,h2,h3,h4,h5,h6 {
    font-weight: 700;
  }
}
</style>
