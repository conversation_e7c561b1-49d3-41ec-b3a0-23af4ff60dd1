<script setup lang="ts">
import {onMounted, ref} from "vue";
import {useI18n} from "vue-i18n";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore} from "@/stores";

import GModal from '@/components/ui/GModal/index.vue';
import GSelect from "@/components/ui/GSelect/index.vue";

const showModal = ref<boolean>(false);

const { locale } = useI18n();
const { videoQualityList } = storeToRefs(useGameStore());
const { langList, lang, mainVolume, soundVolume, bmgVolume, cameraInfo } = storeToRefs(useMainStore());
const { setLang } = useMainStore();

const getSliderBackground = (value: number) => {
  const percentage = value * 100;
  return `linear-gradient(to right, #FF003F 0%, #FF003F ${percentage}%, #656770 ${percentage}%, #656770 100%)`;
};

const handleLang = (lang: string | number) => {
  locale.value = lang as string;
  setLang(lang as string);
}

const show = () => {
  showModal.value = true;
}

const hide = () => {
  showModal.value = false;
}

onMounted(() => {
  show();
});

defineExpose({ show, hide })
</script>

<template>
  <GModal v-model:show="showModal" :title="$t('set_up')">
    <div class="px-1">
      <div class="pb-4 text-white">
        <p class="text-white text-sm">{{ $t('master_volume') }}</p>
        <input type="range" min="0" max="1" step="0.1" v-model="mainVolume"
               :style="{ background: getSliderBackground(mainVolume) }" class="volume-slider" />
      </div>
      <div class="pb-4 text-white">
        <p class="text-white text-sm">{{ $t('sound_effects') }}</p>
        <input type="range" min="0" max="1" step="0.1" v-model="soundVolume"
               :style="{ background: getSliderBackground(soundVolume) }" class="volume-slider" />
      </div>
      <div class="pb-4 text-white">
        <p class="text-white text-sm">{{ $t('background_music') }}</p>
        <input type="range" min="0" max="1" step="0.1" v-model="bmgVolume"
               :style="{ background: getSliderBackground(bmgVolume) }" class="volume-slider" />
      </div>
      <div class="py-4 text-white flex items-center justify-between border-b border-[#302f32]">
        <p class="text-white text-sm">{{ $t('definition') }}</p>
        <GSelect v-model="cameraInfo.quality" :options="videoQualityList.map(item => ({ label: `${item}P`, value: item }))" />
      </div>
      <div class="py-4 text-white flex items-center justify-between">
      <p class="text-white text-sm">{{ $t('language') }}</p>
      <GSelect :value="lang" :options="langList" @change="handleLang" />
    </div>
    </div>
  </GModal>
</template>

<style scoped>
.volume-slider {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  border-radius: 2px;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ffeef3;
  box-shadow: 0 0 4px 1px #ff003f;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ffeef3;
  box-shadow: 0 0 4px 1px #ff003f;
  cursor: pointer;
}
</style>