<template>
  <!-- 使用 transition 组件包裹需要动画的元素 -->
  <transition name="scale-in">
    <div v-show="isVisible" class="w-full absolute top-0 left-0 bg overflow-hidden" style="height: 82%" ref="swiperRef">
      <div class="relitive text-white gap-3">
        <section class="tab-bg flex align-center h-[20px] w-max absolute top-0 left-3" @click="changeTab">
          <div :class="{
            'text-center w-[80px] leading-[20px]': true,
            active: isActive,
          }">
            Top
          </div>
          <div :class="{
            'text-center w-[80px] leading-[20px]': true,
            active: !isActive,
          }">
            Odd and Even
          </div>
        </section>
        <section class="text-center font-normal h-[20px] leading-[20px]">
          {{ $t('trend_history') }}
        </section>
        <section class="absolute top-[4px] right-[5px]">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none" class="cursor-pointer" @click="toggleHistoryShow">
            <rect width="2.29124" height="14.5749" transform="matrix(0.692704 -0.721222 0.704535 0.709669 0 1.65234)"
                  fill="white" />
            <rect width="2.29124" height="14.5749"
                  transform="matrix(-0.692704 -0.721222 -0.704535 0.709669 12 1.65234)" fill="white" />
          </svg>
        </section>
      </div>
      <Loading :show="loading" class="size-full">
        <HistoryTop class="mt-[2px]" v-show="isActive" :historyLists="historyTrend" />
        <HistoryOdd class="mt-[2px]" v-show="!isActive" :hBetPanelShow="hBetPanelShow" :historyLists="historyTrend" />
      </Loading>
    </div>
  </transition>
</template>

<script setup lang="ts">
import {defineProps, nextTick, onMounted, onUnmounted, ref, watch} from "vue";
import { storeToRefs } from "pinia";
import { useMainStore, useGameStore } from "@/stores";
import emitter from "@/utils/emitter";

import HistoryTop from "../widget/HistoryTop.vue";
import HistoryOdd from "../widget/HistoryOdd.vue";
import Loading from "@/components/loading/index.vue"

const { hBetPanelShow } = storeToRefs(useMainStore());
const { toggleHistoryShow } = useMainStore();
const { gameStatus, historyTrend } = storeToRefs(useGameStore())

const swiperRef = ref<any>(null);

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: true,
  },
});

const isActive = ref(true);
const loading = ref(false);

const changeTab = (e: any) => {
  if (e.target.classList.contains("active")) {
    return;
  }
  isActive.value = !isActive.value;
};

const hideLoading = () => {
  loading.value = false;
}

watch(hBetPanelShow, (newValue) => {
  if (!swiperRef.value) return;
  if (!newValue) {
    swiperRef.value.style.height = "90%";
  } else {
    swiperRef.value.style.height = "82%";
  }
});

watch(gameStatus, (status) => {
  if (status === 'game-end' && props.isVisible) {
    toggleHistoryShow()
  }
}, { deep: true, immediate: true });

// 监听 isVisible 属性的变化
watch(
  () => props.isVisible,
  (newValue) => {
    if (newValue) {
      loading.value = true;
      isActive.value = true;
      emitter.emit("get-game-history-req");
      nextTick(() => {
        if (!hBetPanelShow.value) {
          swiperRef.value.style.height = "90%";
        } else {
          swiperRef.value.style.height = "82%";
        }
      });
    }
  }
);

onMounted(() => {
  emitter.on('get-game-history-res', hideLoading);
});

onUnmounted(() => {
  emitter.off('get-game-history-res', hideLoading);
});
</script>

<style lang="scss" scoped>
.bg {
  background: rgba(0, 0, 0, 0.5);
}

.tab-bg {
  background: rgba(255, 255, 255, 0.3);
}

.active {
  background: linear-gradient(180deg, #72001c 0%, #910024 70.3%, #d50035 100%);
  font-weight: 700;
}

/* 定义动画 */
.scale-in-enter-active,
.scale-in-leave-active {
  transition: transform 0.3s ease;
}

.scale-in-enter-from,
.scale-in-leave-to {
  transform: scale(0);
  transform-origin: center center;
}
</style>
