<script setup lang="ts">

</script>

<template>
  <div class="fixed inset-0 size-full flex items-center justify-center bg-black/70 z-9999">
    <div class="maintain-info--inner w-70 layout-x:h-25 layout-y:h-37.5">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path d="M11.9499 3.30048C11.7266 3.77382 11.6099 4.29715 11.6099 4.83715C11.6099 5.80048 11.9833 6.70048 12.6633 7.38048C13.3399 8.05715 14.2433 8.43382 15.2066 8.43382C15.7466 8.43382 16.2699 8.31715 16.7433 8.09048C16.1033 8.73715 15.2433 9.08715 14.3399 9.08715C14.1033 9.08715 13.8666 9.06048 13.6366 9.01382C13.2666 8.93382 12.8733 9.05048 12.6033 9.32048L6.04994 15.8705C6.01661 14.8472 5.18994 14.0238 4.16661 13.9872L10.7133 7.43715C10.9833 7.16715 11.0999 6.78048 11.0199 6.40382C10.7833 5.27382 11.1266 4.11048 11.9466 3.30048L11.9499 3.30048ZM13.0933 13.1772L16.6933 16.7772C16.8933 16.9772 16.8933 17.3038 16.6933 17.5005C16.4966 17.6972 16.1666 17.6972 15.9699 17.5005L12.3699 13.9005L13.0933 13.1772ZM13.8866 1.23048C13.8033 1.23048 13.7166 1.24048 13.6299 1.25048C11.0799 1.65048 9.38328 4.10048 9.91661 6.64048L2.35994 14.2005C1.39661 15.1605 1.39661 16.7238 2.35994 17.6805C3.31661 18.6372 4.88661 18.6372 5.83994 17.6805L13.3999 10.1205C15.9399 10.6505 18.3933 8.96048 18.7866 6.40715C18.8833 5.79382 18.6366 5.44382 18.3133 5.44382C18.1633 5.44382 17.9999 5.51715 17.8399 5.67382L16.9399 6.57382C15.9833 7.53048 14.4133 7.53048 13.4566 6.57382C12.4933 5.61048 12.4933 4.05048 13.4566 3.09048C13.4566 3.09048 13.8599 2.68715 14.3566 2.19048C14.8066 1.74382 14.5833 1.22715 13.8866 1.23048ZM3.51994 16.5205C2.99994 16.0005 3.37994 15.1205 4.09994 15.1205C4.81994 15.1205 5.20661 16.0005 4.68328 16.5205C4.35994 16.8405 3.83994 16.8405 3.51994 16.5205ZM2.35327 1.99715L1.18994 3.15715L2.63994 5.19048L3.85661 5.83048L6.70328 8.67715L7.86328 7.51715L5.02328 4.66715L4.38327 3.45048L2.35327 1.99715ZM12.8033 11.8672L11.0633 13.6072C10.9033 13.7672 10.9033 14.0272 11.0633 14.1872L15.1733 18.2972C15.8133 18.9338 16.8566 18.9372 17.4966 18.2972C18.1366 17.6572 18.1366 16.6138 17.4966 15.9738L13.3866 11.8638C13.2233 11.7038 12.9666 11.7038 12.8033 11.8672Z" fill="white"/>
      </svg>
      <div class="text-white/70 text-xs">{{ $t('the_system_is_under_maintenance') }}</div>
      <div class="back-btn--wrapper layout-x:hidden">
        <button class="back-btn">{{ $t('return_to_lobby') }}</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.maintain-info--inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  border-radius: 8px;
  border: 1px solid rgba(188, 210, 219, 0.70);
  background-color: rgba(251, 251, 251, 0.10);
  backdrop-filter: blur(12px);

  .back-btn--wrapper {
    margin-top: 10px;
    border-radius: 8px;
    padding: 1px;
    background-image: linear-gradient(to bottom, #FF3C6C, #5D0017);
    box-shadow: -1px -1px 1px 0 rgba(0, 0, 0, 0.25) inset, 1px 1px 1px 0 rgba(0, 0, 0, 0.25) inset;
  }

  .back-btn {
    cursor: pointer;
    color: #FFF;
    font-size: 14px;
    font-weight: 700;
    line-height: 11px;
    width: 160px;
    height: 40px;
    border-radius: 8px;
    background-image: linear-gradient(to bottom, #920D3B 0%, #4E142C 100%);
  }
}
</style>