import {createApp, h} from "vue";
import type { MessageOptions } from "./types";

import GMessage from '@/components/ui/GMessage/index.vue';

export function openMessage (props: MessageOptions = {}) {
  const msgContainer = document.createElement('div');
  const root = document.querySelector('#view-box');

  if (!root) return console.error('未找到容器');

  root.appendChild(msgContainer);

  const msg = createApp({
    render() {
      return h(GMessage, {
        ...props,
        onClose: close
      });
    },
  });

  const close = () => {
    msg.unmount();
    msgContainer.remove();
  }

  msg.mount(msgContainer);

  return { close }
}