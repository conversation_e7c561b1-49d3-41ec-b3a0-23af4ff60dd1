<template>
  <div class="mt-[8px] mb-[8px]">
    <div class="history-tab" @click="changeTab">
      <div :class="{ 'tab-btn': true, active: isActive }">Top</div>
      <div :class="{ 'tab-btn': true, active: !isActive }">Odd and Even</div>
    </div>
    <Loading :show="loading" class="history-lists" v-show="isActive">
      <section class="list-item" v-for="item in historyTrend" :key="item.roundId">
        <p class="item-title">NO.{{ item.roundId }}</p>
        <div class="item-imgs">
          <img
            v-for="num in item.result?.split(',')"
            :key="num"
            :src="`/images/ball-${num}.png`"
            alt="ball"
          />
        </div>
      </section>
    </Loading>
    <Loading :show="loading" class="history-lists" v-show="!isActive">
      <table>
        <tr v-for="item in computedOddResult" :key="item.id">
          <td v-for="index in 9" :key="index">
            <template v-if="!item.even">
                <img v-show="item.count >= index" :src="`/images/odd.png`" alt=""/>
            </template>
            <template v-if="item.even">
                <img v-show="item.count >= index" :src="`/images/even.png`" alt=""/>
            </template>
          </td>
          <td >{{ item.count>9?`+${item.count - 9}`:'' }}</td>
        </tr>
      </table>
    </Loading>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, computed, onUnmounted} from "vue";
import {storeToRefs} from "pinia";
import {useGameStore} from "@/stores";
import emitter from "@/utils/emitter.ts";

import Loading from "@/components/loading/index.vue"

const { historyTrend } = storeToRefs(useGameStore());

// 用于控制tab的激活状态
const isActive = ref(true);

const loading = ref(false);

const changeTab = () => {
  isActive.value = !isActive.value;
  loading.value = true;
  emitter.emit("get-game-history-req");
};

// 处理单双数据的函数
const handleOdds = (data: Array<{ even: boolean }>) => {
  if (data.length === 0) return [];
  const result: Array<{id:number,even:boolean,count:number}> = [];
  let count = 0;
  for (let i = 0; i < data.length; i++) {
    if(result.length == 0){
      result.push({id:count,even:data[i].even,count:1});
    }else{
      if(result[result.length-1].even == data[i].even){
        result[result.length-1].count++;
      }else{
        count++;
        result.push({id:count,even:data[i].even,count:1});
      }
    }
  }
  return result;
};

// 使用计算属性监听
const computedOddResult = computed(() => {
  return handleOdds(historyTrend.value);
});

const hideLoading = () => {
  loading.value = false;
}

onMounted(()=>{
  emitter.on('get-game-history-res', hideLoading);
})

onUnmounted(() => {
  emitter.off('get-game-history-res', hideLoading);
})
</script>

<style scoped>
.history-tab {
  width: 240px;
  height: 40px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0 auto;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tab-btn {
  width: 112px;
  height: 32px;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  color: #ffffff;
}
.tab-btn.active {
  background: linear-gradient(180deg, #72001c 0%, #910024 70.3%, #d50035 100%);
  border-radius: 4px;
  font-weight: 700;
}

.history-lists {
  max-height: 420px;
  padding: 0 12px;
  overflow: auto;
}
.list-item {
  border-bottom: 1px solid #292a2c;
  margin: 16px 0;
}
.item-title {
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 12px;
}
.item-imgs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.item-imgs img {
  width: 28px;
  height: 28px;
}

table {
    border-collapse: collapse;
    width: 100%;
    border: 1px solid #363538; /* 添加表格边框 */
    margin-top: 12px;
}

tr {
    border: 1px solid #363538; /* 添加行边框 */
    height: 30px;
}

td {
    width: 30px;
    text-align: center;
    border: 1px solid #363538; /* 添加单元格边框 */
    line-height: 30px;
    color: #ffffff;
    font-size:12px ;
    font-weight:700 ;
}
td:nth-last-child(1){
    width: 50px;
}
td img {
    width: 26px;
    height: 26px;
    margin: 0 auto;
}
</style>
