<script setup lang="ts">
import ToolBar from "@/components/widget/ToolBar.vue";
import TimeInfo from "@/components/widget/TimeInfo.vue";
</script>

<template>
  <header class="mobile-header-nav">
    <div class="flex items-center justify-start">
      <svg width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g>
          <path d="M7.53863e-07 8.37681L12 17L7.05882 11L5.10247 8.37681L7.05882 6L12 0L7.53863e-07 8.37681Z" fill="white"/>
        </g>
      </svg>
    </div>

    <TimeInfo />

    <div class="flex items-center justify-end">
      <ToolBar :toolbar="['rule']" />
    </div>
  </header>
</template>

<style scoped>
.mobile-header-nav {
  display: grid;
  grid-template-columns: minmax(0, 1fr) auto minmax(0, 1fr);
  grid-template-rows: 30px;
  align-items: center;
  width: 100%;
  padding-inline: 12px;
  padding-top: 10px;
  padding-bottom: 5px;
}
</style>