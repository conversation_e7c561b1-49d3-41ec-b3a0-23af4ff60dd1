<script setup lang="ts">
import {ref, onMounted, onUnmounted} from "vue";
import {storeToRefs} from "pinia";
import {useGameStore} from "@/stores";
import emitter from "@/utils/emitter.ts";

import CurrentPeriod from "@/components/widget/CurrentPeriod.vue";
import DragonTigerBet from "@/components/widget/bet/DragonTigerBet.vue";
import BallBet from "@/components/widget/bet/BallBet.vue";
import BetBar from "@/components/widget/bet/BetBar.vue";

const { betChipsResult, historyBallRate } = storeToRefs(useGameStore());
const { setBetChipsResult } = useGameStore();

//玩家下注结果
const betResult = ref({});

const getBetAmount = (res: SocketData) => {
  if (res?.data?.code === 200) {
    betResult.value = {...res.data.data};
    setBetChipsResult({...res.data.data});
  }
}

emitter.on('game-bet-amount', getBetAmount);


onMounted(() => {
  if(Object.keys(betChipsResult.value).length!== 0) {
    betResult.value = {...betChipsResult.value};
  }
});

onUnmounted(() => {
  emitter.off('game-bet-amount', getBetAmount);
});
</script>

<template>
  <div class="px-1 py-2.5">
    <div class="px-2">
      <CurrentPeriod />
    </div>

    <div class="my-2.5 grid grid-cols-10">
      <div v-for="item in 10" :key="item" class="text-center text-white/90">
        <img class="block size-5 mx-auto" :src="`/images/ball-${item}.png`" :alt="`ball ${item}`" />
        <div class="mt-1.5">{{ historyBallRate[item] }}</div>
      </div>
    </div>

    <div class="flex flex-col gap-1.5">
      <DragonTigerBet :betResult="betResult"/>
      <BallBet :betResult="betResult" />
      <BetBar  />
    </div>
  </div>
</template>

<style scoped>

</style>
