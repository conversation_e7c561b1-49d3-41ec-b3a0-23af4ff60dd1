<script setup lang="ts">
import {onMounted, onUnmounted, ref, useTemplateRef} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";

import MobileBetPanel from "@/components/mobile/MobileBetPanel.vue";
import MobileHistoryPanel from "@/components/mobile/MobileHistoryPanel.vue";
import MobileRecordPanel from "@/components/mobile/MobileRecordPanel.vue";
import emitter from "@/utils/emitter.ts";

const { scale } = storeToRefs(useMainStore());

const tabBoxRef = useTemplateRef('tabBoxRef');
const panelBoxRef = useTemplateRef('panelBoxRef');
const tabItemMapRef = ref<{ [key: number]: any }>({});

let cleanupObserver: any = null;

const updateTabs = (idx: number, scroll = true) => {
  const actTab = tabItemMapRef.value?.[idx];
  if (actTab && tabBoxRef.value && panelBoxRef.value) {
    const { x: rootX } = tabBoxRef.value.getBoundingClientRect();
    const { x: itemX, width } = actTab.getBoundingClientRect();
    const offset = (itemX - rootX + width / 2) / scale.value;
    tabBoxRef.value.style.setProperty('--decoration-underline-x', `${offset}px`);
    scroll && panelBoxRef.value.scroll({
      left: idx * panelBoxRef.value.clientWidth,
      behavior: 'smooth'
    });
  }
}

const setupObserver = () => {
  const observer = new IntersectionObserver((entries) => {
    for (const entry of entries) {
      const { target, isIntersecting } = entry;
      if (isIntersecting) {
        const { index } = (target as HTMLElement).dataset;
        if (index === '1') emitter.emit('get-game-history-req');
        if (index === '2') emitter.emit('get-game-history-bet-req');
        updateTabs(Number(index), false);
      }
    }
  }, {
    root: panelBoxRef.value,
    threshold: 1
  });

  const items = panelBoxRef.value?.querySelectorAll('.panels-item') || [];

  items.forEach(target => observer.observe(target));

  return () => observer.disconnect();
}

onMounted(() => {
  cleanupObserver = setupObserver();
  updateTabs(0);
});

onUnmounted(() => {
  cleanupObserver?.();
});
</script>

<template>
  <div>
    <div ref="tabBoxRef" class="tabs-wrapper">
      <div class="flex justify-start w-20">
        <div :ref="el => tabItemMapRef[0] = el" class="tabs-item" @click="updateTabs(0)">
          {{ $t('bets') }}
        </div>
      </div>

      <div class="mx-auto">
        <div :ref="el => tabItemMapRef[1] = el" class="tabs-item" @click="updateTabs(1)">
          {{ $t('trend_history') }}
        </div>
      </div>

      <div class="flex justify-end w-20">
        <div :ref="el => tabItemMapRef[2] = el" class="tabs-item" @click="updateTabs(2)">
          {{ $t('records') }}
        </div>
      </div>
    </div>

    <div ref="panelBoxRef" class="panels-wrapper scrollbar-hidden">
      <section class="panels-item" data-index="0"><MobileBetPanel /></section>
      <section class="panels-item" data-index="1"><MobileHistoryPanel /></section>
      <section class="panels-item" data-index="2"><MobileRecordPanel /></section>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tabs-wrapper {
  --decoration-underline-x: 0px;

  width: 100%;
  overflow-x: auto;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  display: flex;
  justify-content: space-between;

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    content: '';
    width: 16px;
    height: 3px;
    background-color: #FF003F;
    transform: translateX(calc(var(--decoration-underline-x) - 50%));
    transition: transform 0.3s ease;
  }

  .tabs-item {
    flex-shrink: 0;
    padding-inline: 12px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.3s ease;

    &.active {
      font-weight: 700;
      color: rgba(255, 255, 255, 1);
    }
  }
}

.panels-wrapper {
  display: flex;
  overflow-x: scroll;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;

  .panels-item {
    flex-shrink: 0;
    width: 100%;
    scroll-snap-stop: always;
    scroll-snap-align: center;
  }
}
</style>