import { createI18n } from "vue-i18n";
import zh from "./zh.json";
import en from "./en.json";
import hi from "./hi.json";
import ur from "./ur.json";
import bn from "./bn.json";
import tl from "./tl.json";
import id from "./id.json";
import vi from "./vi.json";
import th from "./th.json";
import pt from "./pt.json";
import es from "./es.json";
import ja from "./ja.json";
import ko from "./ko.json";

const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: "en", // 默认语言
  messages: {
    en,
    zh,
    hi,
    ur,
    bn,
    tl,
    id,
    vi,
    th,
    pt,
    es,
    ja,
    ko,
  },
});

export default i18n;