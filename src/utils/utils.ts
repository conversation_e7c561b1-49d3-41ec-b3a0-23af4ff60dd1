import {utils} from "animejs";
import CryptoJS from 'crypto-js';
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";


export const glog = (...args: any[]) => {
  console.log(`glog ==========> [${getTimeText()}]`, ...args);
}

export const getTimeText = (time?: number) => {
  const d =  time ? new Date(time) : new Date();
  const pad2 = utils.padStart(2, '0');
  const pad3 = utils.padStart(3, '0');
  const h = pad2(d.getHours());
  const m = pad2(d.getMinutes());
  const s = pad2(d.getSeconds());
  const ms = pad3(d.getMilliseconds());
  return `${h}:${m}:${s}.${ms}`;
}

export const getCountDownText = (ms: number) => {
  if (ms <= 0) return "00:00:00";

  const pad2 = utils.padStart(2, '0');

  const m = Math.ceil(ms / 1000);
  const hours = Math.floor(m / 3600);
  const minutes = Math.floor(m % 3600 / 60);
  const seconds = Math.floor(m % 60);

  return `${ pad2(hours) }:${ pad2(minutes) }:${ pad2(seconds) }`;
}

export const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    const value = num / 1000000000;
    return Number.isInteger(value) ? `${value}m` : `${value.toFixed(1).replace(/\.0$/, '')}B`;
  }
  if (num >= 100000) {
    const value = num / 100000;
    return Number.isInteger(value) ? `${value}m` : `${value.toFixed(1).replace(/\.0$/, '')}M`;
  }
  if (num >= 1000) {
    const value = num / 1000;
    return Number.isInteger(value) ? `${value}k` : `${value.toFixed(1).replace(/\.0$/, '')}K`;
  }
  return num.toString();
};

export const roundNumber = (num: number) => {
  return Math.round(num * 1e4) / 1e4;
}

export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

export const isMobileDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobileUA = /mobile|android|iphone|ipod|blackberry|windows phone/i.test(userAgent);
  const isSmallScreen = window.innerWidth <= 768 || window.screen.width <= 768;
  return isMobileUA || isSmallScreen || isTouchDevice();
}

export const getBoundingClientRect = (target: Element) => {
  const root = document.querySelector('#view-box');
  if (!root) return {};
  const { scale, isRotation } = storeToRefs(useMainStore());
  const rootRect = root.getBoundingClientRect();
  const targetRect = target.getBoundingClientRect();

  const width = roundNumber(targetRect.width / scale.value);
  const height = roundNumber(targetRect.height / scale.value);
  const left = roundNumber((targetRect.left - rootRect.left) / scale.value);
  const top = roundNumber((targetRect.top - rootRect.top) / scale.value);

  if (!isRotation.value) return { left, top, width, height };

  return {
    width: height,
    height: width,
    left: top,
    top: roundNumber((rootRect.right - targetRect.right) / scale.value),
  }
}

export const openFullscreen = () => {
  const view = document.querySelector("#view-box");
  return view?.requestFullscreen?.();
}

export const closeFullscreen = () => {
  return document.exitFullscreen();
}

export const getKey = (keyStr: string) => {
  // 1. 计算 MD5 哈希
  const md5Hash = CryptoJS.MD5(keyStr).toString(); // 得到 32 位 hex 字符串

  // 2. 截取前 16 个字符作为 key 和 iv
  const keyPart = md5Hash.substring(0, 16);
  const ivPart = md5Hash.substring(0, 16);

  // 3. 转换为 CryptoJS WordArray
  const key = CryptoJS.enc.Utf8.parse(keyPart);
  const iv = CryptoJS.enc.Utf8.parse(ivPart);

  return { key, iv };
}

export const decrypt = (k: string, value: string) => {
  const { key, iv } = getKey(k);

  // 4. Base64 解码加密数据 (CryptoJS expects Base64 input directly)
  const cipherWordArray = CryptoJS.enc.Base64.parse(value);

  // 5. 执行解密
  const decrypted = CryptoJS.AES.decrypt(
    { ciphertext: cipherWordArray } as CryptoJS.lib.CipherParams,
    key,
    { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
  );

  // 6. 转换为明文字符串
  try {
    return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
  } catch (err) {
    console.error(err);
    return {}
  }
}

export const encrypt = (k: string, value: any) => {
  const { key, iv } = getKey(k);

  try {
    const encrypted = CryptoJS.AES.encrypt(
      JSON.stringify(value),
      key,
      { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
    );

    return encrypted.toString();
  } catch (e) {
    console.error(e);
    return "";
  }
}
