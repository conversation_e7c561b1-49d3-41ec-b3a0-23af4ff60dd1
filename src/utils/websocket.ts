interface WebSocketConfig {
    url: string;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    protocols?: string | string[];
}

interface WebSocketEventHandlers {
    onOpen?: (event: Event) => void;
    onMessage?: (data: string | ArrayBuffer | Blob) => void;
    onError?: (error: Event) => void;
    onClose?: (event: CloseEvent) => void;
}

class WebSocketClient {
    private ws: WebSocket | null = null;
    private config: WebSocketConfig;
    private eventHandlers: WebSocketEventHandlers;
    private reconnectAttempts: number = 0;
    private shouldReconnect: boolean = true;

    constructor(config: WebSocketConfig, eventHandlers: WebSocketEventHandlers = {}) {
        this.config = {
            reconnectInterval: 5000,
            maxReconnectAttempts: 5,
            ...config
        };
        this.eventHandlers = eventHandlers;
        this.connect();
    }

    private connect(): void {
        try {
            this.ws = new WebSocket(this.config.url, this.config.protocols);
            this.setupEventListeners();
        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.handleReconnect();
        }
    }

    private setupEventListeners(): void {
        if (!this.ws) return;

        this.ws.onopen = (event: Event) => {
            this.reconnectAttempts = 0;
            this.eventHandlers.onOpen?.(event);
        };

        this.ws.onmessage = (event: MessageEvent) => {
            this.eventHandlers.onMessage?.(event.data);
        };

        this.ws.onerror = (error: Event) => {
            this.eventHandlers.onError?.(error);
        };

        this.ws.onclose = (event: CloseEvent) => {
            this.eventHandlers.onClose?.(event);
            this.handleReconnect();
        };
    }

    private handleReconnect(): void {
        if (
            !this.shouldReconnect ||
            this.reconnectAttempts >= (this.config.maxReconnectAttempts ?? Infinity)
        ) {
            return;
        }

        this.reconnectAttempts++;
        setTimeout(() => {
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})...`);
            this.connect();
        }, this.config.reconnectInterval);
    }

    public send(data: string | ArrayBuffer | Blob): void {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.warn('WebSocket is not connected');
            return;
        }
        this.ws.send(data);
    }

    public close(code?: number, reason?: string): void {
        if (!this.ws) return;
        this.shouldReconnect = false;
        this.ws.close(code, reason);
        this.ws = null;
    }

    public isConnected(): boolean {
        return !!this.ws && this.ws.readyState === WebSocket.OPEN;
    }

    public setEventHandler<K extends keyof WebSocketEventHandlers>(
        event: K,
        handler: WebSocketEventHandlers[K]
    ): void {
        this.eventHandlers[event] = handler;
    }
}

export default WebSocketClient;
