import protobuf from 'protobufjs';

// 定义protobuf消息类型
export interface ISocketResData {
  msgId: string;
  data: Uint8Array;
}

export interface ISocketData {
  currentTimeMillis: number;
  msgId: string;
  path: string;
  serverName: string;
  data?: any;
}

// protobuf根对象和消息类型
let root: protobuf.Root | null = null;
let SocketResDataType: protobuf.Type | null = null;
let SocketDataType: protobuf.Type | null = null;

// 初始化protobuf
export async function initProtobuf(): Promise<void> {
  try {
    // 使用内联定义而不是加载.proto文件
    root = new protobuf.Root();
    
    // 定义SocketResData消息
    const socketResDataType = new protobuf.Type('SocketResData');
    socketResDataType.add(new protobuf.Field('msgId', 1, 'string'));
    socketResDataType.add(new protobuf.Field('data', 2, 'bytes'));
    
    // 定义SocketData消息
    const socketDataType = new protobuf.Type('SocketData');
    socketDataType.add(new protobuf.Field('currentTimeMillis', 1, 'int64'));
    socketDataType.add(new protobuf.Field('msgId', 2, 'string'));
    socketDataType.add(new protobuf.Field('path', 3, 'string'));
    socketDataType.add(new protobuf.Field('serverName', 4, 'string'));
    socketDataType.add(new protobuf.Field('data', 5, 'string')); // 使用string存储JSON数据
    
    // 添加到根命名空间
    root.add(socketResDataType);
    root.add(socketDataType);
    
    // 缓存类型
    SocketResDataType = socketResDataType;
    SocketDataType = socketDataType;
    
    console.log('Protobuf initialized successfully');
  } catch (error) {
    console.error('Failed to initialize protobuf:', error);
    throw error;
  }
}

// 编码SocketResData
export function encodeSocketResData(data: ISocketResData): Uint8Array {
  if (!SocketResDataType) {
    throw new Error('Protobuf not initialized. Call initProtobuf() first.');
  }
  
  const message = SocketResDataType.create(data);
  return SocketResDataType.encode(message).finish();
}

// 解码SocketResData
export function decodeSocketResData(buffer: Uint8Array): ISocketResData {
  if (!SocketResDataType) {
    throw new Error('Protobuf not initialized. Call initProtobuf() first.');
  }
  
  const message = SocketResDataType.decode(buffer);
  return SocketResDataType.toObject(message) as ISocketResData;
}

// 编码SocketData
export function encodeSocketData(data: ISocketData): Uint8Array {
  if (!SocketDataType) {
    throw new Error('Protobuf not initialized. Call initProtobuf() first.');
  }
  
  // 将data字段序列化为JSON字符串
  const dataToEncode = {
    ...data,
    data: data.data ? JSON.stringify(data.data) : undefined
  };
  
  const message = SocketDataType.create(dataToEncode);
  return SocketDataType.encode(message).finish();
}

// 解码SocketData
export function decodeSocketData(buffer: Uint8Array): ISocketData {
  if (!SocketDataType) {
    throw new Error('Protobuf not initialized. Call initProtobuf() first.');
  }
  
  const message = SocketDataType.decode(buffer);
  const decoded = SocketDataType.toObject(message) as any;
  
  // 将data字段从JSON字符串反序列化
  if (decoded.data) {
    try {
      decoded.data = JSON.parse(decoded.data);
    } catch (error) {
      console.warn('Failed to parse data field as JSON:', error);
    }
  }
  
  return decoded as ISocketData;
}

// 检查protobuf是否已初始化
export function isProtobufInitialized(): boolean {
  return root !== null && SocketResDataType !== null && SocketDataType !== null;
}

// 将ArrayBuffer转换为Uint8Array
export function arrayBufferToUint8Array(buffer: ArrayBuffer): Uint8Array {
  return new Uint8Array(buffer);
}

// 将Uint8Array转换为ArrayBuffer
export function uint8ArrayToArrayBuffer(uint8Array: Uint8Array): ArrayBuffer {
  return uint8Array.buffer.slice(uint8Array.byteOffset, uint8Array.byteOffset + uint8Array.byteLength);
}

// Base64编码
export function encodeBase64(uint8Array: Uint8Array): string {
  const binary = Array.from(uint8Array, byte => String.fromCharCode(byte)).join('');
  return btoa(binary);
}

// Base64解码
export function decodeBase64(base64: string): Uint8Array {
  const binary = atob(base64);
  return new Uint8Array(Array.from(binary, char => char.charCodeAt(0)));
}
