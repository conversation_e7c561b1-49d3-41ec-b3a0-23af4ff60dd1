import mitt, { type Emitter } from 'mitt';

type GameEvent = {
  /** 连接socket初始化 */
  'socket-init': SocketData;
  /** 等待游戏可以开始下注 */
  'game-wait': SocketData;
  /** 开始下注 */
  'game-bet': SocketData;
  /** 下注结束 */
  'game-bet-end': SocketData;
  /** 游戏开始 */
  'game-start': SocketData;
  /** 游戏结束 */
  'game-end': SocketData;
  /** 游戏暂停 */
  'game-paused': SocketData;
  /** 获得比赛结果 */
  'game-settlement': SocketData;
  /** 接收中奖结果 */
  'game-award': SocketData;
  /** 各区域投注金额 */
  'game-bet-amount': SocketData;

  /** 用户投注请求 */
  'user-bet-req': any;
  /** 用户投注响应 */
  'user-bet-res': SocketData;

  /** 用户撤销请求 */
  'user-revoke-req': any;
  /** 用户撤销响应 */
  'user-revoke-res': SocketData;

  /** 获取当前期次用户投注结果请求 */
  'get-player-bet-req': void;
  /** 获取当前期次用户投注结果响应 */
  'get-player-bet-res': SocketData;

  /** 获取游戏配置信息请求 */
  'get-game-config-req': void;
  /** 获取游戏配置信息返回 */
  'get-game-config-res': SocketData;

  /** 获取用户余额请求 */
  'get-user-balance-req': void;
  /** 获取用户余额响应 */
  'get-user-balance-res': SocketData;

  /** 下注最后三秒强提示 */
  'play-bet-prompt': void;

  /** 播放转场动画 */
  'play-switch-scenes': void;
  /** 转场动画开始 */
  'show-switch-scenes': void;
  /** 转场动画结束 */
  'hide-switch-scenes': void;

  /** 重新初始化视频播放器 */
  'video-reload': void;

  /** 开始视频流 */
  'video-stream-start': void;
  /** 停止视频流 */
  'video-stream-stop': void;

  /** 展示视频loading */
  'show-video-loading': void;
  /** 隐藏视频loading */
  'hide-video-loading': void;

  /** 获取游戏历史结果请求 */
  'get-game-history-req': void;
  /** 获取游戏历史结果返回 */
  'get-game-history-res': SocketData;

  /** 获取游戏历史下注记录请求 */
  'get-game-history-bet-req': void;
  /** 获取游戏历史下注记录返回 */
  'get-game-history-bet-res': SocketData;

  /** 获取游戏规则请求 */
  'get-game-rules-req': { language: string };
  /** 获取游戏规则返回 */
  'get-game-rules-res': SocketData;

  /** 播放音效 */
  'play-sound': string;

  /** 更新下注总金额 */
  'update-bet-total-amount': any;

};

const emitter: Emitter<GameEvent> = mitt<GameEvent>();

export default emitter;