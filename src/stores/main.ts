import { computed, reactive, ref } from "vue";
import { defineStore } from 'pinia';
import { CameraType } from "@/constant/gameData";

type CameraInfo = {
  type: CameraType,
  quality: number;
}

export const useMainStore = defineStore('main', () => {

  const scale = ref<number>(1);
  const setScale = (newScale: number) => {
    scale.value = newScale;
  }

  // 设备当前方向
  const orientation = ref<'landscape' | 'portrait'>('portrait');
  const setOrientation = (newOrientation: 'landscape' | 'portrait') => {
    orientation.value = newOrientation;
  }

  // 只有移动端才有是否旋转（表示在移动端手动横屏）
  const isRotation = ref<boolean>(false);
  const toggleIsRotation = (status?: boolean) => {
    isRotation.value = status ?? !isRotation.value;
  }

  const mainVolume = ref(1);
  const setMainVolume = (newVolume: number) => {
    mainVolume.value = newVolume;
  }

  const soundVolume = ref(1);
  const setSoundVolume = (newVolume: number) => {
    soundVolume.value = newVolume;
  }

  const bmgVolume = ref(1);
  const setBmgVolume = (newVolume: number) => {
    bmgVolume.value = newVolume;
  }

  const layout = ref<'x' | 'y'>('x');
  const isVertical = computed(() => layout.value === 'y');
  const isHorizontal = computed(() => layout.value === 'x');
  const setLayout = (newLayout: 'x' | 'y') => {
    layout.value = newLayout;
  }

  const langList = ref([
    { label: "中文", value: "zh" },
    { label: "English", value: "en" },
    { label: "हिंदी", value: "hi" },
    { label: 'ur', value: "ur" },
    { label: "বাঙালি", value: "bn" },
    { label: "tl", value: "tl" },
    { label: "Indonesia", value: "id" },
    { label: "vi", value: "vi" },
    { label: "th", value: "th" },
    { label: "pt", value: "pt" },
    { label: "Español", value: "es" },
    { label: "日本語", value: "ja" },
    { label: "한국인", value: "ko" },
  ]);

  const lang = ref('en');
  const setLang = (newLang: string) => {
    lang.value = newLang;
  }

  const hBetPanelShow = ref<boolean>(false);
  const toggleBetPanelShow = (status?: boolean) => {
    hBetPanelShow.value = status ?? !hBetPanelShow.value;
  }

  const isShowHistory = ref<boolean>(false);
  const toggleHistoryShow = () => {
    isShowHistory.value =!isShowHistory.value;
  }

  const isShowVideoLoading = ref<boolean>(false);
  const toggleVideoLoading = (show?: boolean) => {
    isShowVideoLoading.value = show ?? !isShowVideoLoading.value;
  }

  const cameraInfo = reactive<CameraInfo>({
    type: CameraType.A,
    quality: 480,
  });

  const toggleCameraType = (type?: CameraType) => {
    if (type) {
      cameraInfo.type = type;
      return;
    }
    if (cameraInfo.type === CameraType.A) {
      cameraInfo.type = CameraType.B;
      return;
    }
    if (cameraInfo.type === CameraType.B) {
      cameraInfo.type = CameraType.A;
      return;
    }
  }

  const setCameraQuality = (quality: number) => {
    cameraInfo.quality = quality;
  }

  return {
    scale, setScale,
    orientation, setOrientation,
    isRotation, toggleIsRotation,
    mainVolume, setMainVolume,
    bmgVolume, setBmgVolume,
    soundVolume, setSoundVolume,
    layout, isVertical, isHorizontal, setLayout,
    hBetPanelShow, toggleBetPanelShow,
    langList, lang, setLang,
    isShowHistory, toggleHistoryShow,
    isShowVideoLoading, toggleVideoLoading,
    cameraInfo, toggleCameraType, setCameraQuality,
  };
})