import {computed, ref} from "vue";
import { defineStore } from "pinia";
import { GameStatus, SocketEventCode } from "@/constant/gameData.ts";


type VideoStream = {
    title: string;
    url: string;
    resolution: number;
}

type VideoBlock = {
    [key in number]: { a?: VideoStream; b?: VideoStream; }
}

type VideoStreamData = {
    domestic: VideoBlock;
    international: VideoBlock;
}

type BetChipsResult = {
    betList?: string;
    originBetList?: string;
}

type GameTypeData = {
    betTitle: any,
    playTypeId: string,
    playTypeName: string,
    rate: any,
    sort: number,
    status: boolean,
}

type HistoryTrend = {
    roundId: string;
    result: string;
    even: boolean;
    num: string;
}

const SocketStatusToGameStatusMap: Partial<{ [key in SocketEventCode]: GameStatus }> = {
    [SocketEventCode.WAIT]: GameStatus.GAME_WAIT,
    [SocketEventCode.BET]: GameStatus.GAME_BET,
    [SocketEventCode.BET_END]: GameStatus.GAME_BET_END,
    [SocketEventCode.GAME_START]: GameStatus.GAME_START,
    [SocketEventCode.GAME_END]: GameStatus.GAME_END,
    [SocketEventCode.SETTLEMENT]: GameStatus.GAME_SETTLEMENT,
}

export const useGameStore = defineStore("game", () => {
    // 投注筹码列表
    const betChipsList = ref<number[]>([]);

    // 播放流列表
    const videoStreamList = ref<VideoStreamData>({
        domestic: {},  //国内
        international: {}, //国外
    });

    const videoQualityList = computed(() => {
        return Object
          .keys(videoStreamList.value.domestic || {})
          .map(Number)
          .sort((a, b) => a - b);
    })

    // 服务器时间
    const serverTime = ref<number>(0);

    // 游戏状态
    const gameStatus = ref<GameStatus>();

    // 游戏玩法
    const gamePlayType = ref<GameTypeData[]>();

    // 游戏规则
    const gameRules = ref("");

    // 游戏id
    const gameId = ref<string>("");

    // 期数
    const gameRoundId = ref<string>("000");

    // 游戏投注结束时间
    const gameBetEndTime = ref<number>();

    // 等待下一场游戏开始间隔时间
    const gameNextWaitEndTime = ref<number>();

    // 比赛结果
    const gameResult = ref<string[]>(['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']);

    // 本期获奖金额
    const gameAwardAmount = ref(0);

    // 当前使用的筹码索引
    const activeChipIndex = ref(2);
    // 投注结果
    const betChipsResult = ref({});
    // 历史球号胜率
    const historyBallRate = ref<Record<string, string>>({});
    // 历史趋势记录
    const historyTrend = ref<HistoryTrend[]>([]);
    // 历史投注结果
    const historyBetResult = ref<any[]>([]);
    //本期玩家投注冠军结果
    const playerBetChipsResult = ref<any>({});
    // 本期玩家投注龙虎结果
    const playerBetDTResult = ref<any>({});
    // 投注获胜信息
    const gameWinBet = ref({
        type1Loc: null,
        type2Loc: null,
        type3Loc: null,
        type4Loc: null
    })

    const setGameRules = (rule: string) => {
        gameRules.value = rule;
    }

    const setServerTime = (time: number) => {
        serverTime.value = time;
    }

    const setGameId = (id: string) => {
        gameId.value = id;
    }

    const setGameRoundId = (id: string) => {
        gameRoundId.value = id;
    }

    /**
     * 更新游戏投注结束时间（本地时间）
     * @param curr 服务器当前时间
     * @param end 服务器结束时间
     */
    const setGameBetEndTime = (curr: number, end: number) => {
        const diff = end - curr;
        if (diff > 0) {
            gameBetEndTime.value = Date.now() + diff;
        } else {
            gameBetEndTime.value = undefined;
        }
    }

    const cleanGameBetEndTime = () => {
        gameBetEndTime.value = undefined;
    }

    /**
     * 更新下一场游戏开始需要等待的时间
     * @param curr 服务器当前时间
     * @param end 服务器结束时间
     */
    const setGameNextWaitEndTime = (curr: number, end: number) => {
        const diff = end - curr;
        if (diff > 0) {
            gameNextWaitEndTime.value = Date.now() + diff;
        } else {
            gameNextWaitEndTime.value = undefined;
        }
    }

    const cleanGameNextWaitEndTime = () => {
        gameNextWaitEndTime.value = undefined;
    }

    const setGameStatus = (status: GameStatus) => {
        gameStatus.value = status;
    }

    const setActiveChipIndex = (index: number) => {
        activeChipIndex.value = index
    }

    const setGameResult = (result: any) => {
        gameResult.value = (result ?? "").split(",");
    };

    const setGameAwardAmount = (amount: number) => {
        gameAwardAmount.value = amount;
    }

    const cleanGameAwardAmount = () => {
        gameAwardAmount.value = 0;
    }

    const setGameWinBet = (result: any) => {
        gameWinBet.value = {
            type1Loc: result.type1Loc,
            type2Loc: result.type2Loc,
            type3Loc: result.type3Loc,
            type4Loc: result.type4Loc
        };
    }

    // 根据socket返回的数据更新游戏相关的数据
    const updateGameInfo = ({ currentTimeMillis, path, data: resData }: SocketData) => {
        if (currentTimeMillis < serverTime.value) return;
        const { code, data } = resData;
        setServerTime(currentTimeMillis);
        if (code === 200) {
            if (data?.gameId) setGameId(data.gameId);
            if (data?.roundId) setGameRoundId(data.roundId);
            if (data?.result) setGameResult(data.result);
            if (data?.betTime || data?.endBetTime) setGameBetEndTime(currentTimeMillis, data.betTime || data.endBetTime);
            if (data?.gapTimes) setGameNextWaitEndTime(currentTimeMillis, currentTimeMillis + data.gapTimes * 1000);
            // 结算时保存获胜信息
            if (path === SocketEventCode.SETTLEMENT) setGameWinBet(data)
            if (data?.totalAwardAmount) setGameAwardAmount(data.totalAwardAmount)
            if (data?.notice) setGameRules(data.notice)
            if (Reflect.has(SocketStatusToGameStatusMap, path)) {
                gameStatus.value = SocketStatusToGameStatusMap[path as SocketEventCode];
            }
        }
    }

    // 设置视频流地址
    const setVideoStreamList = (data: any) => {
        const format = (data: VideoStream[]) => {
            const result: { [key in number]: any } = {};
            data.forEach(item => {
                if (!result[item.resolution]) {
                    result[item.resolution] = {};
                }
                if (item.url.includes('a_')) {
                    result[item.resolution]['a'] = item;
                } else {
                    result[item.resolution]['b'] = item;
                }
            });
            return result;
        }
        videoStreamList.value = {
            domestic: format(data?.domestic || []),
            international: format(data?.international || []),
        };
    }

    // 设置筹码列表
    const setBetChipsList = (data: BetChipsResult) => {
        const betData = data?.betList || data?.originBetList || '0.1,0.5,1,2,5,10';
        betChipsList.value = betData.split(',').map(Number);
    }

    // 设置游戏玩法
    const setGamePlayType = (list: any) => {
        gamePlayType.value = list;
    }

    //设置投注结果
    const setBetChipsResult = (data: any) => {
        betChipsResult.value = data;
    }

    //设置玩家投注冠军结果
    const setPlayerBetChipsResult = (data: any) => {
        playerBetChipsResult.value = data
    }
    //设置玩家投注龙虎结果
    const setPlayerBetDTResult = (data: any) => {
        playerBetDTResult.value = data
    }
    //设置历史投注结果
    const setHistoryBetResult = (data: any) => {
        historyBetResult.value = data
    }
    // 设置历史球号胜率
    const setHistoryBallRate = (data: Record<string, string>) => {
        historyBallRate.value = data;
    }
    // 设置历史趋势记录
    const setHistoryTrend = (data: HistoryTrend[]) => {
        historyTrend.value = data;
    }

    return {
        gameRules, setGameRules,
        gameStatus, setGameStatus,
        gameId, setGameId,
        gameRoundId, setGameRoundId,
        gameBetEndTime, setGameBetEndTime, cleanGameBetEndTime,
        gameNextWaitEndTime, setGameNextWaitEndTime, cleanGameNextWaitEndTime,
        activeChipIndex, setActiveChipIndex,
        gameResult, setGameResult,
        gameWinBet, setGameWinBet,
        updateGameInfo,
        videoStreamList, videoQualityList, setVideoStreamList,
        gameAwardAmount, setGameAwardAmount, cleanGameAwardAmount,
        betChipsList, setBetChipsList,
        gamePlayType, setGamePlayType,
        betChipsResult, setBetChipsResult,
        playerBetChipsResult, setPlayerBetChipsResult,
        playerBetDTResult, setPlayerBetDTResult,
        historyBetResult, setHistoryBetResult,
        historyBallRate, setHistoryBallRate,
        historyTrend, setHistoryTrend,
    };
}, {
    persist: {
        key: 'playerBetResult',
        pick:['playerBetChipsResult','playerBetDTResult','betChipsResult','gameRoundId'],
    }
});