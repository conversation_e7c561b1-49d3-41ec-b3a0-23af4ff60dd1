<script setup lang="ts">
import {RouterView} from "vue-router";
import {useI18n} from "vue-i18n";
import {useGameStore, useMainStore, useUserStore} from "@/stores";
import {getGameAddress, login} from "@/api";

const { setLang } = useMainStore();
const { setGameId } = useGameStore();
const { setToken, setMerchantId, setCurrency } = useUserStore();

const { locale } = useI18n();

const setupQueryParams = async () => {
  // const loginResult = await login({ currency: 'USD', lang: 'en', userId: '810850360' });
  // const userToken = loginResult?.data?.tokenValue;
  // const gameResult = await getGameAddress({"clientIp":"1","currency":"USD","gameId":"100201","lang":"en","merchantId":"1","merchantUserId":"810850360"})
  // console.log(userToken)

  const search = window.location.search.replace('?', '');
  const params = search ? search.split('&') : [];
  
  const queryMap = params.reduce((curr: any, next: string) => {
    const [key, value] = next.split("=");
    curr[key] = value;
    return curr;
  }, {});

  const lang = queryMap.lang ?? "en";
  const gameId = queryMap.gameId ?? "100201";
  const currency = queryMap.currency ?? "USD";
  const merchantId = queryMap.merchantId ?? "1";
  const token = queryMap.token ?? "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxOTMwOTE0NTY5Nzc4MzQ4MDM0Iiwicm5TdHIiOiJRMUpuWm5KdEVJMVdxS25IYk53ZnJmQmI1bHZHM29DNSIsIkdBTUVfVVNFUl9JTkZPIjp7ImlkIjoiMTkzMDkxNDU2OTc3ODM0ODAzNCIsIm1lcmNoYW50VXNlcmlkIjoiOTk5ODg4IiwidXNlcm5hbWUiOiJnZGVnc2QiLCJsb2dpblRpbWUiOjE3NDkyMDA4ODIsImlwYWRkciI6IjEiLCJ1c2VyVHlwZSI6NCwibWVyY2hhbnRJZCI6IjEiLCJjdXJyZW5jeSI6IlVTRCIsImdhbWVJZCI6IjEwMDIwMSIsImNsaWVudElwIjoiMSJ9LCJNRVJDSEFOVF9JRCI6IjEifQ.vRUlGrDy747PHOsQuueFis7A-kn9Yd4IqpGA5mXZY5M";

  setLang(lang as string);
  setGameId(gameId as string);
  setToken(token as string);
  setCurrency(currency as string);
  setMerchantId(merchantId as string);

  locale.value = lang as string;
}

// 初始化通过url传入的参数
setupQueryParams();
</script>

<template>
  <RouterView />
</template>

<style scoped></style>
