<script setup lang="ts">
import {RouterView} from "vue-router";
import {useI18n} from "vue-i18n";
import {useGameStore, useMainStore, useUserStore} from "@/stores";
import {getGameAddress, login} from "@/api";

const { setLang } = useMainStore();
const { setGameId } = useGameStore();
const { setToken, setMerchantId, setCurrency } = useUserStore();

const { locale } = useI18n();

const setupQueryParams = async () => {
  // const loginResult = await login({ currency: 'USD', lang: 'en', userId: '810850360' });
  // const userToken = loginResult?.data?.tokenValue;
  // const gameResult = await getGameAddress({"clientIp":"1","currency":"USD","gameId":"100201","lang":"en","merchantId":"1","merchantUserId":"810850360"})
  // console.log(userToken)

  const search = window.location.search.replace('?', '');
  const params = search ? search.split('&') : [];
  
  const queryMap = params.reduce((curr: any, next: string) => {
    const [key, value] = next.split("=");
    curr[key] = value;
    return curr;
  }, {});

  const lang = queryMap.lang ?? "en";
  const gameId = queryMap.gameId ?? "100201";
  const currency = queryMap.currency ?? "USD";
  const merchantId = queryMap.merchantId ?? "1";
  const token = queryMap.token ?? "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxOTMxMjg1MjI5OTY4OTgyMDE3Iiwicm5TdHIiOiJkMTBxbGR2VlJwMlliVzV6RDExZnJTbXZ3dGp0THBXWSIsIkdBTUVfVVNFUl9JTkZPIjp7ImlkIjoiMTkzMTI4NTIyOTk2ODk4MjAxNyIsIm1lcmNoYW50VXNlcmlkIjoiODEwODUwMzYwMSIsInVzZXJuYW1lIjoiMzJleDJ3IiwibG9naW5UaW1lIjoxNzQ5Mjg5MjU1LCJpcGFkZHIiOiIxIiwidXNlclR5cGUiOjQsIm1lcmNoYW50SWQiOiIxIiwiY3VycmVuY3kiOiJVU0QiLCJpY29uIjoiVVNEIiwiZ2FtZUlkIjoiMTAwMjAxIiwiY2xpZW50SXAiOiIxIn0sIk1FUkNIQU5UX0lEIjoiMSJ9.lK5ueGDCblm4ur7bjfCY-75SuapgRshyZ8BxZJ7wF5s";

  setLang(lang as string);
  setGameId(gameId as string);
  setToken(token as string);
  setCurrency(currency as string);
  setMerchantId(merchantId as string);

  locale.value = lang as string;
}

// 初始化通过url传入的参数
setupQueryParams();
</script>

<template>
  <RouterView />
</template>

<style scoped></style>
