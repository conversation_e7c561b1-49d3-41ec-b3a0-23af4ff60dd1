import {onUnmounted, ref} from 'vue';
import { md5 } from 'js-md5';
import { v4 as uuidv4 } from 'uuid';
import {SocketEventCode} from '@/constant/gameData.ts';
import {decrypt, encrypt, getTimeText, glog} from '@/utils/utils';

const SERVER_NAME = 'game-live-pingballs';

export function useWebSocket(url: string, callback: (ws: WebSocket) => void) {
  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)

  const maxReconnectAttempts = 5
  const handlers = new Map<SocketEventCode, MessageHandler>()

  let reconnectTimer: any = null

  // 初始化连接
  const connect = () => {
    socket.value = new WebSocket(url)

    socket.value.onopen = () => {
      isConnected.value = true
      reconnectAttempts.value = 0
      console.log('WebSocket connected')
      callback(socket.value!);
    }

    socket.value.onmessage = (event) => {
      try {
        const { msgId, data } = JSON.parse(event.data) as SocketResData;
        const message: SocketData = decrypt(msgId, data);

        if (message?.path === SocketEventCode.PING) {
          send(SocketEventCode.PONG);
          return;
        }

        const handler = handlers.get(message?.path as SocketEventCode);
        handler?.(message);

        console.log(`[${getTimeText()}]-[${getTimeText(message.currentTimeMillis)}]WebSocket message received:`, message)
      } catch (error) {
        console.error('WebSocket message parsing error:', error)
      }
    }

    socket.value.onclose = () => {
      isConnected.value = false
      console.log('WebSocket disconnected')
      attemptReconnect()
    }

    socket.value.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }

  // 重连逻辑
  const attemptReconnect = () => {
    if (reconnectAttempts.value < maxReconnectAttempts) {
      reconnectAttempts.value++

      const delay = Math.min(1000 * reconnectAttempts.value, 5000) // 指数退避

      console.log(`Attempting to reconnect in ${delay}ms...`)

      reconnectTimer = setTimeout(connect, delay)
    } else {
      console.log('Max reconnection attempts reached')
    }
  }

  // 发送消息
  const send = (path: SocketEventCode, data?: any) => {
    glog('WebSocket send:', path, data);
    if (isConnected.value && socket.value) {
      const msgId = md5(`${uuidv4()}_${path}_${Date.now()}`);
      const sendData = { path, msgId, data, serverName: SERVER_NAME };
      const sendBody = { msgId, data: encrypt(msgId, sendData) };
      socket.value.send(JSON.stringify(sendBody));
      return msgId;
    } else {
      console.error('WebSocket is not connected');
    }
  }

  // 订阅消息
  const subscribe = (eventCode: SocketEventCode, handler: MessageHandler) => {
    handlers.set(eventCode, handler)
  }

  // 取消订阅
  const unsubscribe = (eventCode: SocketEventCode) => {
    handlers.delete(eventCode)
  }

  // 关闭连接
  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
  }

  // 自动清理
  onUnmounted(() => {
    disconnect()
  })

  window.addEventListener('beforeunload', () => {
    disconnect()
  })

  return {
    socket,
    isConnected,
    connect,
    disconnect,
    send,
    subscribe,
    unsubscribe
  }
}